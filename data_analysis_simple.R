# 简化版健康行为数据分析
# 避免复杂包依赖，专注于核心分析

# 加载基础库
library(readxl)
library(tidyverse)

cat("开始数据分析...\n")

# 1. 数据读取和预处理 ---------------------------------------------------------------

kid_data <- read.csv("processed_data.csv")
bmi_data <- read.csv("processed_data_bmi.csv")
parent_data <- read.csv("processed_data_parents.csv")

# 合并数据
data <- kid_data %>%
  left_join(bmi_data %>% select(学校名称, 年级, 班级, 学生姓名, 年龄),
            by = c("学校名称", "年级", "班级", "学生姓名"),
            suffix = c("", "_bmi")) %>%
  left_join(parent_data,
            by = c("学校名称" = "school",
                   "年级" = "Kidgrade", 
                   "学生姓名" = "Kidname"),
            suffix = c("", "_parent")) %>%
  select(-ends_with("_bmi"), -ends_with(".y"))

cat("数据合并完成！\n")

# 读取BMI标准并计算健康状况
bmi_standard <- readxl::read_excel("BMI判断标准.xls")

data <- data %>%
  mutate(
    身高 = ifelse(身高 > 2, 身高 / 100, 身高),
    体重 = ifelse(体重 >= 200, 体重 / 2, 体重),
    BMI = round(体重 / (身高^2), 1)
  ) %>%
  left_join(bmi_standard, by = c("性别", "年龄")) %>%
  mutate(
    healthy = if_else(BMI > 消瘦标准 & BMI < 超重标准, "正常", "异常", 
                      missing = NA_character_),
    overweight = case_when(
      BMI >= 超重标准 ~ "是",
      is.na(BMI) ~ NA_character_,
      TRUE ~ "否"
    )
  )

# 行为变量转换
data <- data %>%
  mutate(
    # E1-E4转换
    food_intake_type1 = case_when(E1 == 1 ~ 1, E1 == 2 ~ 0, TRUE ~ NA_real_),
    food_intake_type2 = case_when(E2 == 1 ~ 1, E2 == 2 ~ 0, TRUE ~ NA_real_),
    food_intake_type3 = case_when(E3 == 1 ~ 1, E3 == 2 ~ 0, TRUE ~ NA_real_),
    food_intake_type4 = case_when(E4 == 1 ~ 1, E4 == 2 ~ 0, TRUE ~ NA_real_),
    
    # F1-F14转换（F8、F12-F14反向计分）
    food_intake_freq1 = F1,
    food_intake_freq2 = F2,
    food_intake_freq3 = F3,
    food_intake_freq4 = F4,
    food_intake_freq5 = F5,
    food_intake_freq6 = F6,
    food_intake_freq7 = F7,
    food_intake_freq8 = case_when(
      F8 == 1 ~ 5, F8 == 2 ~ 4, F8 == 3 ~ 3, F8 == 4 ~ 2, F8 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq9 = F9,
    food_intake_freq10 = F10,
    food_intake_freq11 = F11,
    food_intake_freq12 = case_when(
      F12 == 1 ~ 5, F12 == 2 ~ 4, F12 == 3 ~ 3, F12 == 4 ~ 2, F12 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq13 = case_when(
      F13 == 1 ~ 5, F13 == 2 ~ 4, F13 == 3 ~ 3, F13 == 4 ~ 2, F13 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq14 = case_when(
      F14 == 1 ~ 5, F14 == 2 ~ 4, F14 == 3 ~ 3, F14 == 4 ~ 2, F14 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    
    # G1-G4转换
    water_drink = case_when(
      G1 == 2 ~ 1, G1 %in% c(1, 3, 4) ~ 0, G1 == 5 ~ NA_real_, TRUE ~ NA_real_
    ),
    outdoor_excise = case_when(
      G2 %in% c(1, 2) ~ 0, G2 %in% c(3, 4, 5) ~ 1, TRUE ~ NA_real_
    ),
    nutrition_competition = case_when(
      G3 == 1 ~ 1, G3 == 2 ~ 0, TRUE ~ NA_real_
    ),
    weight_monitoring = case_when(
      G4 == 1 ~ 1, G4 == 2 ~ 0, TRUE ~ NA_real_
    )
  )

# 基本情况变量转换
data <- data %>%
  mutate(
    年级 = factor(case_when(
      年级 == 3 ~ "3年级", 年级 == 4 ~ "4年级", 年级 == 5 ~ "5年级",
      TRUE ~ NA_character_
    ), levels = c("3年级", "4年级", "5年级")),
    性别 = factor(case_when(
      性别 == 1 ~ "男", 性别 == 2 ~ "女", TRUE ~ NA_character_
    ), levels = c("男", "女")),
    主要照料者 = factor(case_when(
      A1 == 1 ~ "父母", A1 == 2 ~ "祖辈", A1 == 3 ~ "其他", TRUE ~ NA_character_
    ), levels = c("父母", "祖辈", "其他")),
    主要照料者学历 = factor(case_when(
      A2 == 1 ~ "小学及以下", A2 == 2 ~ "初中", A2 == 3 ~ "高中", 
      A2 == 4 ~ "大专", A2 == 5 ~ "本科及以上", TRUE ~ NA_character_
    ), levels = c("小学及以下", "初中", "高中", "大专", "本科及以上")),
    家庭经济条件 = factor(case_when(
      A3 == 2 ~ "低", A3 == 1 & A4 <= 2 ~ "中", A3 == 1 & A4 > 2 ~ "高",
      TRUE ~ NA_character_
    ), levels = c("低", "中", "高"))
  )

# 计算总分和二分类变量
data <- data %>%
  mutate(
    food_intake_freq_total = rowSums(
      select(., food_intake_freq1:food_intake_freq14), na.rm = TRUE
    ),
    healthy_binary = case_when(
      healthy == "正常" ~ 1, healthy == "异常" ~ 0, TRUE ~ NA_real_
    )
  )

# 保存处理后的数据
write.csv(data, "outputs/cleaned_data_NL&behavior.csv", row.names = FALSE)
cat("数据预处理完成！\n")

# 2. 描述性分析 -----------------------------------------------------------------------

cat("开始描述性分析...\n")

# 创建输出目录
if (!dir.exists("outputs")) {
  dir.create("outputs")
}

f_questions <- paste0("F", 1:14)
g_questions <- paste0("G", 1:4)

# F1-14频次分析
cat("分析F1-14各题频次...\n")
f_freq_results <- list()
for (f_var in f_questions) {
  if (f_var %in% names(data)) {
    freq_table <- data %>%
      count(.data[[f_var]], name = "计数") %>%
      mutate(
        题目 = f_var,
        选项 = .data[[f_var]],
        占比 = round(计数 / sum(计数) * 100, 2)
      ) %>%
      select(题目, 选项, 计数, 占比)
    f_freq_results[[f_var]] <- freq_table
  }
}
f_all_freq <- do.call(rbind, f_freq_results)
write.csv(f_all_freq, "outputs/f_questions_frequency.csv", row.names = FALSE)

# G1-4频次分析
cat("分析G1-4各题频次...\n")
g_freq_results <- list()
for (g_var in g_questions) {
  if (g_var %in% names(data)) {
    freq_table <- data %>%
      count(.data[[g_var]], name = "计数") %>%
      mutate(
        题目 = g_var,
        选项 = .data[[g_var]],
        占比 = round(计数 / sum(计数) * 100, 2)
      ) %>%
      select(题目, 选项, 计数, 占比)
    g_freq_results[[g_var]] <- freq_table
  }
}
g_all_freq <- do.call(rbind, g_freq_results)
write.csv(g_all_freq, "outputs/g_questions_frequency.csv", row.names = FALSE)

# food_intake_freq总分分布
cat("分析food_intake_freq总分分布...\n")
shapiro_test <- shapiro.test(data$food_intake_freq_total)
is_normal <- shapiro_test$p.value > 0.05

freq_total_stats <- data.frame(
  变量 = "food_intake_freq总分",
  样本量 = sum(!is.na(data$food_intake_freq_total)),
  正态性检验p值 = round(shapiro_test$p.value, 3),
  分布类型 = ifelse(is_normal, "正态分布", "非正态分布"),
  均值 = round(mean(data$food_intake_freq_total, na.rm = TRUE), 2),
  标准差 = round(sd(data$food_intake_freq_total, na.rm = TRUE), 2),
  中位数 = round(median(data$food_intake_freq_total, na.rm = TRUE), 2),
  四分位间距 = round(IQR(data$food_intake_freq_total, na.rm = TRUE), 2)
)

write.csv(freq_total_stats, "outputs/freq_total_distribution.csv", 
          row.names = FALSE)

cat(sprintf("food_intake_freq总分: %s (p=%.3f)\n", 
            ifelse(is_normal, "服从正态分布", "不服从正态分布"),
            shapiro_test$p.value))

cat("描述性分析完成！\n")

# 3. 组间差异分析 -----------------------------------------------------------------------

cat("开始组间差异分析...\n")

group_vars <- c("年级", "性别", "主要照料者", "主要照料者学历", "家庭经济条件")

# 简化的组间差异分析
for (group_var in group_vars) {
  cat(sprintf("分析%s组间差异...\n", group_var))

  # F题目组间差异
  f_group_summary <- data %>%
    select(all_of(c(f_questions, group_var))) %>%
    filter(!is.na(.data[[group_var]])) %>%
    group_by(.data[[group_var]]) %>%
    summarise(
      across(all_of(f_questions),
             list(mean = ~ mean(.x, na.rm = TRUE),
                  sd = ~ sd(.x, na.rm = TRUE),
                  n = ~ sum(!is.na(.x))),
             .names = "{.col}_{.fn}"),
      .groups = "drop"
    )

  write.csv(f_group_summary,
            sprintf("outputs/F_questions_%s_summary.csv", group_var),
            row.names = FALSE)

  # food_intake_freq总分组间差异
  freq_total_group_summary <- data %>%
    select(food_intake_freq_total, all_of(group_var)) %>%
    filter(!is.na(.data[[group_var]])) %>%
    group_by(.data[[group_var]]) %>%
    summarise(
      样本量 = sum(!is.na(food_intake_freq_total)),
      均值 = mean(food_intake_freq_total, na.rm = TRUE),
      标准差 = sd(food_intake_freq_total, na.rm = TRUE),
      中位数 = median(food_intake_freq_total, na.rm = TRUE),
      四分位间距 = IQR(food_intake_freq_total, na.rm = TRUE),
      .groups = "drop"
    )

  write.csv(freq_total_group_summary,
            sprintf("outputs/freq_total_%s_summary.csv", group_var),
            row.names = FALSE)

  # G题目组间差异
  g_group_summary <- data %>%
    select(all_of(c(g_questions, group_var))) %>%
    filter(!is.na(.data[[group_var]])) %>%
    group_by(.data[[group_var]]) %>%
    summarise(
      across(all_of(g_questions),
             list(mean = ~ mean(.x, na.rm = TRUE),
                  sd = ~ sd(.x, na.rm = TRUE),
                  n = ~ sum(!is.na(.x))),
             .names = "{.col}_{.fn}"),
      .groups = "drop"
    )

  write.csv(g_group_summary,
            sprintf("outputs/G_questions_%s_summary.csv", group_var),
            row.names = FALSE)
}

cat("组间差异分析完成！\n")

# 4. 简单相关性分析 -----------------------------------------------------------------------

cat("开始相关性分析...\n")

# 4.1 营养素养与F1-14各行为题目的简单相关分析
cat("分析营养素养与F1-14各行为题目的相关性...\n")

if ("Total_Core_Information" %in% names(data)) {
  f_correlation_results <- data.frame(
    题目 = character(),
    相关系数 = numeric(),
    p值 = numeric(),
    显著性 = character(),
    stringsAsFactors = FALSE
  )

  for (f_var in f_questions) {
    if (f_var %in% names(data)) {
      clean_data <- data %>%
        select(Total_Core_Information, all_of(f_var)) %>%
        na.omit()

      if (nrow(clean_data) > 10) {
        cor_test <- cor.test(clean_data$Total_Core_Information,
                            clean_data[[f_var]],
                            method = "spearman")

        significance <- case_when(
          cor_test$p.value < 0.001 ~ "***",
          cor_test$p.value < 0.01 ~ "**",
          cor_test$p.value < 0.05 ~ "*",
          TRUE ~ ""
        )

        f_correlation_results <- rbind(f_correlation_results, data.frame(
          题目 = f_var,
          相关系数 = round(cor_test$estimate, 3),
          p值 = round(cor_test$p.value, 3),
          显著性 = significance,
          stringsAsFactors = FALSE
        ))
      }
    }
  }

  write.csv(f_correlation_results,
            "outputs/nutrition_literacy_f_correlation.csv",
            row.names = FALSE)
}

# 4.2 营养素养与food_intake_freq总分的相关性
cat("分析营养素养与food_intake_freq总分的相关性...\n")

if ("Total_Core_Information" %in% names(data)) {
  clean_data <- data %>%
    select(Total_Core_Information, food_intake_freq_total) %>%
    na.omit()

  if (nrow(clean_data) > 10) {
    pearson_cor <- cor.test(clean_data$Total_Core_Information,
                           clean_data$food_intake_freq_total,
                           method = "pearson")

    spearman_cor <- cor.test(clean_data$Total_Core_Information,
                            clean_data$food_intake_freq_total,
                            method = "spearman")

    total_correlation_results <- data.frame(
      方法 = c("Pearson", "Spearman"),
      相关系数 = c(round(pearson_cor$estimate, 3),
                  round(spearman_cor$estimate, 3)),
      p值 = c(round(pearson_cor$p.value, 3),
             round(spearman_cor$p.value, 3)),
      显著性 = c(
        case_when(
          pearson_cor$p.value < 0.001 ~ "***",
          pearson_cor$p.value < 0.01 ~ "**",
          pearson_cor$p.value < 0.05 ~ "*",
          TRUE ~ ""
        ),
        case_when(
          spearman_cor$p.value < 0.001 ~ "***",
          spearman_cor$p.value < 0.01 ~ "**",
          spearman_cor$p.value < 0.05 ~ "*",
          TRUE ~ ""
        )
      ),
      stringsAsFactors = FALSE
    )

    write.csv(total_correlation_results,
              "outputs/nutrition_literacy_total_correlation.csv",
              row.names = FALSE)
  }
}

# 4.3 营养素养与二分类行为变量的相关性
cat("分析营养素养与二分类行为变量的相关性...\n")

binary_behavior_vars <- c("water_drink", "outdoor_excise",
                         "nutrition_competition", "weight_monitoring")

if ("Total_Core_Information" %in% names(data)) {
  binary_correlation_results <- data.frame(
    变量 = character(),
    相关系数 = numeric(),
    p值 = numeric(),
    显著性 = character(),
    stringsAsFactors = FALSE
  )

  for (var in binary_behavior_vars) {
    if (var %in% names(data)) {
      clean_data <- data %>%
        select(Total_Core_Information, all_of(var)) %>%
        na.omit()

      if (nrow(clean_data) > 10) {
        cor_test <- cor.test(clean_data$Total_Core_Information,
                            clean_data[[var]],
                            method = "spearman")

        significance <- case_when(
          cor_test$p.value < 0.001 ~ "***",
          cor_test$p.value < 0.01 ~ "**",
          cor_test$p.value < 0.05 ~ "*",
          TRUE ~ ""
        )

        binary_correlation_results <- rbind(binary_correlation_results,
                                           data.frame(
          变量 = var,
          相关系数 = round(cor_test$estimate, 3),
          p值 = round(cor_test$p.value, 3),
          显著性 = significance,
          stringsAsFactors = FALSE
        ))
      }
    }
  }

  write.csv(binary_correlation_results,
            "outputs/nutrition_literacy_binary_correlation.csv",
            row.names = FALSE)
}

# 4.4 健康行为与healthy的相关性分析
cat("分析健康行为与healthy的相关性...\n")

# F1-14各行为题目与healthy的相关性
f_healthy_correlation_results <- data.frame(
  题目 = character(),
  相关系数 = numeric(),
  p值 = numeric(),
  显著性 = character(),
  stringsAsFactors = FALSE
)

for (f_var in f_questions) {
  if (f_var %in% names(data) && "healthy_binary" %in% names(data)) {
    clean_data <- data %>%
      select(healthy_binary, all_of(f_var)) %>%
      na.omit()

    if (nrow(clean_data) > 10) {
      cor_test <- cor.test(clean_data$healthy_binary,
                          clean_data[[f_var]],
                          method = "spearman")

      significance <- case_when(
        cor_test$p.value < 0.001 ~ "***",
        cor_test$p.value < 0.01 ~ "**",
        cor_test$p.value < 0.05 ~ "*",
        TRUE ~ ""
      )

      f_healthy_correlation_results <- rbind(f_healthy_correlation_results,
                                            data.frame(
        题目 = f_var,
        相关系数 = round(cor_test$estimate, 3),
        p值 = round(cor_test$p.value, 3),
        显著性 = significance,
        stringsAsFactors = FALSE
      ))
    }
  }
}

write.csv(f_healthy_correlation_results,
          "outputs/f_behavior_healthy_correlation.csv",
          row.names = FALSE)

# food_intake_freq总分与healthy的相关性
if ("healthy_binary" %in% names(data)) {
  clean_data <- data %>%
    select(food_intake_freq_total, healthy_binary) %>%
    na.omit()

  if (nrow(clean_data) > 10) {
    cor_test <- cor.test(clean_data$food_intake_freq_total,
                        clean_data$healthy_binary,
                        method = "spearman")

    significance <- case_when(
      cor_test$p.value < 0.001 ~ "***",
      cor_test$p.value < 0.01 ~ "**",
      cor_test$p.value < 0.05 ~ "*",
      TRUE ~ ""
    )

    total_healthy_correlation <- data.frame(
      变量 = "food_intake_freq_total",
      相关系数 = round(cor_test$estimate, 3),
      p值 = round(cor_test$p.value, 3),
      显著性 = significance,
      stringsAsFactors = FALSE
    )

    write.csv(total_healthy_correlation,
              "outputs/freq_total_healthy_correlation.csv",
              row.names = FALSE)

    cat(sprintf("food_intake_freq总分与healthy的相关系数: %.3f (p=%.3f) %s\n",
                cor_test$estimate, cor_test$p.value, significance))
  }
}

cat("相关性分析完成！\n")

# 5. 分析总结 -----------------------------------------------------------------------

cat("\n=== 简化版健康行为分析完成摘要 ===\n")
cat("1. 数据预处理已完成\n")
cat("   - 数据合并和BMI计算\n")
cat("   - 行为变量转换（F8、F12-F14反向计分）\n")
cat("   - 基本情况变量转换\n")
cat("2. 描述性分析已完成\n")
cat("   - F1-14及G1-4各题频次分析\n")
cat("   - food_intake_freq总分分布分析\n")
cat("3. 组间差异分析已完成\n")
cat("   - 各分组变量的组间差异描述性统计\n")
cat("4. 简单相关性分析已完成\n")
cat("   - 营养素养与F1-14各题相关性\n")
cat("   - 营养素养与food_intake_freq总分相关性\n")
cat("   - 营养素养与二分类行为变量相关性\n")
cat("   - 健康行为与healthy相关性\n")
cat("\n注意：此版本为简化分析，未包含校准因素的偏相关分析\n")
cat("所有结果已保存至outputs文件夹\n")

cat("简化版数据分析完成！\n")
