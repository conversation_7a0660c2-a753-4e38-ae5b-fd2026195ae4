<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<style>body{background-color:white;}</style>


</head>
<body>
<div id="hsijaaigck" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;">
  <style>#hsijaaigck table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#hsijaaigck thead, #hsijaaigck tbody, #hsijaaigck tfoot, #hsijaaigck tr, #hsijaaigck td, #hsijaaigck th {
  border-style: none;
}

#hsijaaigck p {
  margin: 0;
  padding: 0;
}

#hsijaaigck .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#hsijaaigck .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#hsijaaigck .gt_title {
  color: #333333;
  font-size: 125%;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#hsijaaigck .gt_subtitle {
  color: #333333;
  font-size: 85%;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#hsijaaigck .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#hsijaaigck .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#hsijaaigck .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#hsijaaigck .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#hsijaaigck .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#hsijaaigck .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#hsijaaigck .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#hsijaaigck .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#hsijaaigck .gt_spanner_row {
  border-bottom-style: hidden;
}

#hsijaaigck .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#hsijaaigck .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#hsijaaigck .gt_from_md > :first-child {
  margin-top: 0;
}

#hsijaaigck .gt_from_md > :last-child {
  margin-bottom: 0;
}

#hsijaaigck .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#hsijaaigck .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#hsijaaigck .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#hsijaaigck .gt_row_group_first td {
  border-top-width: 2px;
}

#hsijaaigck .gt_row_group_first th {
  border-top-width: 2px;
}

#hsijaaigck .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#hsijaaigck .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#hsijaaigck .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#hsijaaigck .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#hsijaaigck .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#hsijaaigck .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#hsijaaigck .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#hsijaaigck .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#hsijaaigck .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#hsijaaigck .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#hsijaaigck .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#hsijaaigck .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#hsijaaigck .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#hsijaaigck .gt_left {
  text-align: left;
}

#hsijaaigck .gt_center {
  text-align: center;
}

#hsijaaigck .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#hsijaaigck .gt_font_normal {
  font-weight: normal;
}

#hsijaaigck .gt_font_bold {
  font-weight: bold;
}

#hsijaaigck .gt_font_italic {
  font-style: italic;
}

#hsijaaigck .gt_super {
  font-size: 65%;
}

#hsijaaigck .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#hsijaaigck .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#hsijaaigck .gt_indent_1 {
  text-indent: 5px;
}

#hsijaaigck .gt_indent_2 {
  text-indent: 10px;
}

#hsijaaigck .gt_indent_3 {
  text-indent: 15px;
}

#hsijaaigck .gt_indent_4 {
  text-indent: 20px;
}

#hsijaaigck .gt_indent_5 {
  text-indent: 25px;
}
</style>
  <table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false">
  <thead>
    <tr class="gt_col_headings gt_spanner_row">
      <th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;题目&lt;/strong&gt;"><strong>题目</strong></th>
      <th class="gt_center gt_columns_top_border gt_column_spanner_outer" rowspan="1" colspan="3" scope="colgroup" id="&lt;strong&gt;主要照料者&lt;/strong&gt;">
        <span class="gt_column_spanner"><strong>主要照料者</strong></span>
      </th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;p-value&lt;/strong&gt;&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;2&lt;/sup&gt;&lt;/span&gt;"><strong>p-value</strong><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span></th>
    </tr>
    <tr class="gt_col_headings">
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;父母&lt;/strong&gt;, N = 2,052&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>父母</strong>, N = 2,052<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;祖辈&lt;/strong&gt;, N = 326&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>祖辈</strong>, N = 326<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;其他&lt;/strong&gt;, N = 45&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>其他</strong>, N = 45<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
    </tr>
  </thead>
  <tbody class="gt_table_body">
    <tr><td headers="label" class="gt_row gt_left">F1</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.572</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">1,813.0 (88.4%)</td>
<td headers="stat_2" class="gt_row gt_center">289.0 (88.7%)</td>
<td headers="stat_3" class="gt_row gt_center">38.0 (84.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">135.0 (6.6%)</td>
<td headers="stat_2" class="gt_row gt_center">19.0 (5.8%)</td>
<td headers="stat_3" class="gt_row gt_center">3.0 (6.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">62.0 (3.0%)</td>
<td headers="stat_2" class="gt_row gt_center">10.0 (3.1%)</td>
<td headers="stat_3" class="gt_row gt_center">3.0 (6.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">26.0 (1.3%)</td>
<td headers="stat_2" class="gt_row gt_center">7.0 (2.1%)</td>
<td headers="stat_3" class="gt_row gt_center">0.0 (0.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">15.0 (0.7%)</td>
<td headers="stat_2" class="gt_row gt_center">1.0 (0.3%)</td>
<td headers="stat_3" class="gt_row gt_center">1.0 (2.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F2</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.868</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">1,286.0 (62.7%)</td>
<td headers="stat_2" class="gt_row gt_center">196.0 (60.1%)</td>
<td headers="stat_3" class="gt_row gt_center">32.0 (71.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">300.0 (14.6%)</td>
<td headers="stat_2" class="gt_row gt_center">49.0 (15.0%)</td>
<td headers="stat_3" class="gt_row gt_center">6.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">245.0 (11.9%)</td>
<td headers="stat_2" class="gt_row gt_center">44.0 (13.5%)</td>
<td headers="stat_3" class="gt_row gt_center">3.0 (6.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">181.0 (8.8%)</td>
<td headers="stat_2" class="gt_row gt_center">28.0 (8.6%)</td>
<td headers="stat_3" class="gt_row gt_center">3.0 (6.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">39.0 (1.9%)</td>
<td headers="stat_2" class="gt_row gt_center">9.0 (2.8%)</td>
<td headers="stat_3" class="gt_row gt_center">1.0 (2.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F3</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.841</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">1,304.0 (63.6%)</td>
<td headers="stat_2" class="gt_row gt_center">203.0 (62.3%)</td>
<td headers="stat_3" class="gt_row gt_center">27.0 (60.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">280.0 (13.7%)</td>
<td headers="stat_2" class="gt_row gt_center">47.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">10.0 (22.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">245.0 (11.9%)</td>
<td headers="stat_2" class="gt_row gt_center">37.0 (11.3%)</td>
<td headers="stat_3" class="gt_row gt_center">5.0 (11.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">148.0 (7.2%)</td>
<td headers="stat_2" class="gt_row gt_center">28.0 (8.6%)</td>
<td headers="stat_3" class="gt_row gt_center">2.0 (4.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">74.0 (3.6%)</td>
<td headers="stat_2" class="gt_row gt_center">11.0 (3.4%)</td>
<td headers="stat_3" class="gt_row gt_center">1.0 (2.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F4</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.369</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">1,180.0 (57.5%)</td>
<td headers="stat_2" class="gt_row gt_center">195.0 (59.8%)</td>
<td headers="stat_3" class="gt_row gt_center">27.0 (60.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">349.0 (17.0%)</td>
<td headers="stat_2" class="gt_row gt_center">47.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">5.0 (11.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">300.0 (14.6%)</td>
<td headers="stat_2" class="gt_row gt_center">54.0 (16.6%)</td>
<td headers="stat_3" class="gt_row gt_center">4.0 (8.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">164.0 (8.0%)</td>
<td headers="stat_2" class="gt_row gt_center">21.0 (6.4%)</td>
<td headers="stat_3" class="gt_row gt_center">7.0 (15.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">58.0 (2.8%)</td>
<td headers="stat_2" class="gt_row gt_center">9.0 (2.8%)</td>
<td headers="stat_3" class="gt_row gt_center">2.0 (4.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F5</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.842</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">1,048.0 (51.1%)</td>
<td headers="stat_2" class="gt_row gt_center">170.0 (52.1%)</td>
<td headers="stat_3" class="gt_row gt_center">24.0 (53.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">336.0 (16.4%)</td>
<td headers="stat_2" class="gt_row gt_center">44.0 (13.5%)</td>
<td headers="stat_3" class="gt_row gt_center">7.0 (15.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">313.0 (15.3%)</td>
<td headers="stat_2" class="gt_row gt_center">50.0 (15.3%)</td>
<td headers="stat_3" class="gt_row gt_center">7.0 (15.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">245.0 (11.9%)</td>
<td headers="stat_2" class="gt_row gt_center">42.0 (12.9%)</td>
<td headers="stat_3" class="gt_row gt_center">3.0 (6.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">109.0 (5.3%)</td>
<td headers="stat_2" class="gt_row gt_center">20.0 (6.1%)</td>
<td headers="stat_3" class="gt_row gt_center">4.0 (8.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F6</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.727</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">397.0 (19.4%)</td>
<td headers="stat_2" class="gt_row gt_center">64.0 (19.6%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (20.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">262.0 (12.8%)</td>
<td headers="stat_2" class="gt_row gt_center">42.0 (12.9%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (17.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">516.0 (25.2%)</td>
<td headers="stat_2" class="gt_row gt_center">73.0 (22.4%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (17.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">551.0 (26.9%)</td>
<td headers="stat_2" class="gt_row gt_center">84.0 (25.8%)</td>
<td headers="stat_3" class="gt_row gt_center">11.0 (24.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">325.0 (15.8%)</td>
<td headers="stat_2" class="gt_row gt_center">63.0 (19.3%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (20.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F7</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.509</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">749.0 (36.5%)</td>
<td headers="stat_2" class="gt_row gt_center">121.0 (37.1%)</td>
<td headers="stat_3" class="gt_row gt_center">15.0 (33.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">332.0 (16.2%)</td>
<td headers="stat_2" class="gt_row gt_center">59.0 (18.1%)</td>
<td headers="stat_3" class="gt_row gt_center">4.0 (8.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">417.0 (20.3%)</td>
<td headers="stat_2" class="gt_row gt_center">59.0 (18.1%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (20.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">375.0 (18.3%)</td>
<td headers="stat_2" class="gt_row gt_center">58.0 (17.8%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (20.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">178.0 (8.7%)</td>
<td headers="stat_2" class="gt_row gt_center">29.0 (8.9%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (17.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F8</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.365</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">102.0 (5.0%)</td>
<td headers="stat_2" class="gt_row gt_center">19.0 (5.8%)</td>
<td headers="stat_3" class="gt_row gt_center">3.0 (6.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">157.0 (7.7%)</td>
<td headers="stat_2" class="gt_row gt_center">21.0 (6.4%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (17.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">266.0 (13.0%)</td>
<td headers="stat_2" class="gt_row gt_center">39.0 (12.0%)</td>
<td headers="stat_3" class="gt_row gt_center">4.0 (8.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">837.0 (40.8%)</td>
<td headers="stat_2" class="gt_row gt_center">136.0 (41.7%)</td>
<td headers="stat_3" class="gt_row gt_center">18.0 (40.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">689.0 (33.6%)</td>
<td headers="stat_2" class="gt_row gt_center">111.0 (34.0%)</td>
<td headers="stat_3" class="gt_row gt_center">12.0 (26.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F9</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.093</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">450.0 (21.9%)</td>
<td headers="stat_2" class="gt_row gt_center">71.0 (21.8%)</td>
<td headers="stat_3" class="gt_row gt_center">12.0 (26.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">372.0 (18.1%)</td>
<td headers="stat_2" class="gt_row gt_center">54.0 (16.6%)</td>
<td headers="stat_3" class="gt_row gt_center">11.0 (24.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">526.0 (25.6%)</td>
<td headers="stat_2" class="gt_row gt_center">81.0 (24.8%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (17.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">494.0 (24.1%)</td>
<td headers="stat_2" class="gt_row gt_center">71.0 (21.8%)</td>
<td headers="stat_3" class="gt_row gt_center">6.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">209.0 (10.2%)</td>
<td headers="stat_2" class="gt_row gt_center">49.0 (15.0%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (17.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F10</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.929</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">283.0 (13.8%)</td>
<td headers="stat_2" class="gt_row gt_center">41.0 (12.6%)</td>
<td headers="stat_3" class="gt_row gt_center">7.0 (15.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">296.0 (14.4%)</td>
<td headers="stat_2" class="gt_row gt_center">49.0 (15.0%)</td>
<td headers="stat_3" class="gt_row gt_center">6.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">552.0 (26.9%)</td>
<td headers="stat_2" class="gt_row gt_center">81.0 (24.8%)</td>
<td headers="stat_3" class="gt_row gt_center">11.0 (24.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">612.0 (29.8%)</td>
<td headers="stat_2" class="gt_row gt_center">108.0 (33.1%)</td>
<td headers="stat_3" class="gt_row gt_center">12.0 (26.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">308.0 (15.0%)</td>
<td headers="stat_2" class="gt_row gt_center">47.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (20.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F11</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.736</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">256.0 (12.5%)</td>
<td headers="stat_2" class="gt_row gt_center">44.0 (13.5%)</td>
<td headers="stat_3" class="gt_row gt_center">5.0 (11.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">252.0 (12.3%)</td>
<td headers="stat_2" class="gt_row gt_center">46.0 (14.1%)</td>
<td headers="stat_3" class="gt_row gt_center">4.0 (8.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">436.0 (21.3%)</td>
<td headers="stat_2" class="gt_row gt_center">59.0 (18.1%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (20.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">637.0 (31.1%)</td>
<td headers="stat_2" class="gt_row gt_center">92.0 (28.2%)</td>
<td headers="stat_3" class="gt_row gt_center">16.0 (35.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">470.0 (22.9%)</td>
<td headers="stat_2" class="gt_row gt_center">85.0 (26.1%)</td>
<td headers="stat_3" class="gt_row gt_center">11.0 (24.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F12</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.061</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">82.0 (4.0%)</td>
<td headers="stat_2" class="gt_row gt_center">16.0 (4.9%)</td>
<td headers="stat_3" class="gt_row gt_center">5.0 (11.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">120.0 (5.9%)</td>
<td headers="stat_2" class="gt_row gt_center">22.0 (6.7%)</td>
<td headers="stat_3" class="gt_row gt_center">3.0 (6.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">229.0 (11.2%)</td>
<td headers="stat_2" class="gt_row gt_center">48.0 (14.7%)</td>
<td headers="stat_3" class="gt_row gt_center">6.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">823.0 (40.1%)</td>
<td headers="stat_2" class="gt_row gt_center">105.0 (32.2%)</td>
<td headers="stat_3" class="gt_row gt_center">14.0 (31.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">797.0 (38.9%)</td>
<td headers="stat_2" class="gt_row gt_center">135.0 (41.4%)</td>
<td headers="stat_3" class="gt_row gt_center">17.0 (37.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F13</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.448</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">90.0 (4.4%)</td>
<td headers="stat_2" class="gt_row gt_center">15.0 (4.6%)</td>
<td headers="stat_3" class="gt_row gt_center">4.0 (8.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">146.0 (7.1%)</td>
<td headers="stat_2" class="gt_row gt_center">26.0 (8.0%)</td>
<td headers="stat_3" class="gt_row gt_center">1.0 (2.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">292.0 (14.2%)</td>
<td headers="stat_2" class="gt_row gt_center">56.0 (17.2%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (20.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">796.0 (38.8%)</td>
<td headers="stat_2" class="gt_row gt_center">113.0 (34.7%)</td>
<td headers="stat_3" class="gt_row gt_center">17.0 (37.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">727.0 (35.4%)</td>
<td headers="stat_2" class="gt_row gt_center">116.0 (35.6%)</td>
<td headers="stat_3" class="gt_row gt_center">14.0 (31.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F14</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.631</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">96.0 (4.7%)</td>
<td headers="stat_2" class="gt_row gt_center">19.0 (5.8%)</td>
<td headers="stat_3" class="gt_row gt_center">4.0 (8.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">150.0 (7.3%)</td>
<td headers="stat_2" class="gt_row gt_center">28.0 (8.6%)</td>
<td headers="stat_3" class="gt_row gt_center">2.0 (4.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">308.0 (15.0%)</td>
<td headers="stat_2" class="gt_row gt_center">50.0 (15.3%)</td>
<td headers="stat_3" class="gt_row gt_center">10.0 (22.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">826.0 (40.3%)</td>
<td headers="stat_2" class="gt_row gt_center">128.0 (39.3%)</td>
<td headers="stat_3" class="gt_row gt_center">14.0 (31.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">671.0 (32.7%)</td>
<td headers="stat_2" class="gt_row gt_center">101.0 (31.0%)</td>
<td headers="stat_3" class="gt_row gt_center">15.0 (33.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
  </tbody>
  
  <tfoot class="gt_footnotes">
    <tr>
      <td class="gt_footnote" colspan="5"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span> n (%)</td>
    </tr>
    <tr>
      <td class="gt_footnote" colspan="5"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span> Pearson’s Chi-squared test</td>
    </tr>
  </tfoot>
</table>
</div>
</body>
</html>
