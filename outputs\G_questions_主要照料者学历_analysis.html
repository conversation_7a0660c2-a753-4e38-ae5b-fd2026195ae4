<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<style>body{background-color:white;}</style>


</head>
<body>
<div id="xpovxskagl" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;">
  <style>#xpovxskagl table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#xpovxskagl thead, #xpovxskagl tbody, #xpovxskagl tfoot, #xpovxskagl tr, #xpovxskagl td, #xpovxskagl th {
  border-style: none;
}

#xpovxskagl p {
  margin: 0;
  padding: 0;
}

#xpovxskagl .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#xpovxskagl .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#xpovxskagl .gt_title {
  color: #333333;
  font-size: 125%;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#xpovxskagl .gt_subtitle {
  color: #333333;
  font-size: 85%;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#xpovxskagl .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#xpovxskagl .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#xpovxskagl .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#xpovxskagl .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#xpovxskagl .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#xpovxskagl .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#xpovxskagl .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#xpovxskagl .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#xpovxskagl .gt_spanner_row {
  border-bottom-style: hidden;
}

#xpovxskagl .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#xpovxskagl .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#xpovxskagl .gt_from_md > :first-child {
  margin-top: 0;
}

#xpovxskagl .gt_from_md > :last-child {
  margin-bottom: 0;
}

#xpovxskagl .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#xpovxskagl .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#xpovxskagl .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#xpovxskagl .gt_row_group_first td {
  border-top-width: 2px;
}

#xpovxskagl .gt_row_group_first th {
  border-top-width: 2px;
}

#xpovxskagl .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#xpovxskagl .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#xpovxskagl .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#xpovxskagl .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#xpovxskagl .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#xpovxskagl .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#xpovxskagl .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#xpovxskagl .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#xpovxskagl .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#xpovxskagl .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#xpovxskagl .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#xpovxskagl .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#xpovxskagl .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#xpovxskagl .gt_left {
  text-align: left;
}

#xpovxskagl .gt_center {
  text-align: center;
}

#xpovxskagl .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#xpovxskagl .gt_font_normal {
  font-weight: normal;
}

#xpovxskagl .gt_font_bold {
  font-weight: bold;
}

#xpovxskagl .gt_font_italic {
  font-style: italic;
}

#xpovxskagl .gt_super {
  font-size: 65%;
}

#xpovxskagl .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#xpovxskagl .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#xpovxskagl .gt_indent_1 {
  text-indent: 5px;
}

#xpovxskagl .gt_indent_2 {
  text-indent: 10px;
}

#xpovxskagl .gt_indent_3 {
  text-indent: 15px;
}

#xpovxskagl .gt_indent_4 {
  text-indent: 20px;
}

#xpovxskagl .gt_indent_5 {
  text-indent: 25px;
}
</style>
  <table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false">
  <thead>
    <tr class="gt_col_headings gt_spanner_row">
      <th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;题目&lt;/strong&gt;"><strong>题目</strong></th>
      <th class="gt_center gt_columns_top_border gt_column_spanner_outer" rowspan="1" colspan="5" scope="colgroup" id="&lt;strong&gt;主要照料者学历&lt;/strong&gt;">
        <span class="gt_column_spanner"><strong>主要照料者学历</strong></span>
      </th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;p-value&lt;/strong&gt;&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;2&lt;/sup&gt;&lt;/span&gt;"><strong>p-value</strong><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span></th>
    </tr>
    <tr class="gt_col_headings">
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;小学及以下&lt;/strong&gt;, N = 118&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>小学及以下</strong>, N = 118<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;初中&lt;/strong&gt;, N = 153&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>初中</strong>, N = 153<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;高中&lt;/strong&gt;, N = 277&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>高中</strong>, N = 277<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;大专&lt;/strong&gt;, N = 521&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>大专</strong>, N = 521<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;本科及以上&lt;/strong&gt;, N = 1,354&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>本科及以上</strong>, N = 1,354<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
    </tr>
  </thead>
  <tbody class="gt_table_body">
    <tr><td headers="label" class="gt_row gt_left">G1</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.071</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">23.0 (19.5%)</td>
<td headers="stat_2" class="gt_row gt_center">37.0 (24.2%)</td>
<td headers="stat_3" class="gt_row gt_center">81.0 (29.2%)</td>
<td headers="stat_4" class="gt_row gt_center">149.0 (28.6%)</td>
<td headers="stat_5" class="gt_row gt_center">355.0 (26.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">31.0 (26.3%)</td>
<td headers="stat_2" class="gt_row gt_center">32.0 (20.9%)</td>
<td headers="stat_3" class="gt_row gt_center">59.0 (21.3%)</td>
<td headers="stat_4" class="gt_row gt_center">103.0 (19.8%)</td>
<td headers="stat_5" class="gt_row gt_center">324.0 (23.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">36.0 (30.5%)</td>
<td headers="stat_2" class="gt_row gt_center">46.0 (30.1%)</td>
<td headers="stat_3" class="gt_row gt_center">67.0 (24.2%)</td>
<td headers="stat_4" class="gt_row gt_center">145.0 (27.8%)</td>
<td headers="stat_5" class="gt_row gt_center">377.0 (27.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">17.0 (14.4%)</td>
<td headers="stat_2" class="gt_row gt_center">29.0 (19.0%)</td>
<td headers="stat_3" class="gt_row gt_center">51.0 (18.4%)</td>
<td headers="stat_4" class="gt_row gt_center">64.0 (12.3%)</td>
<td headers="stat_5" class="gt_row gt_center">185.0 (13.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">11.0 (9.3%)</td>
<td headers="stat_2" class="gt_row gt_center">9.0 (5.9%)</td>
<td headers="stat_3" class="gt_row gt_center">19.0 (6.9%)</td>
<td headers="stat_4" class="gt_row gt_center">60.0 (11.5%)</td>
<td headers="stat_5" class="gt_row gt_center">113.0 (8.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">G2</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.026</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">30.0 (25.4%)</td>
<td headers="stat_2" class="gt_row gt_center">31.0 (20.3%)</td>
<td headers="stat_3" class="gt_row gt_center">53.0 (19.1%)</td>
<td headers="stat_4" class="gt_row gt_center">102.0 (19.6%)</td>
<td headers="stat_5" class="gt_row gt_center">195.0 (14.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">37.0 (31.4%)</td>
<td headers="stat_2" class="gt_row gt_center">66.0 (43.1%)</td>
<td headers="stat_3" class="gt_row gt_center">88.0 (31.8%)</td>
<td headers="stat_4" class="gt_row gt_center">171.0 (32.8%)</td>
<td headers="stat_5" class="gt_row gt_center">483.0 (35.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">27.0 (22.9%)</td>
<td headers="stat_2" class="gt_row gt_center">27.0 (17.6%)</td>
<td headers="stat_3" class="gt_row gt_center">63.0 (22.7%)</td>
<td headers="stat_4" class="gt_row gt_center">123.0 (23.6%)</td>
<td headers="stat_5" class="gt_row gt_center">325.0 (24.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">8.0 (6.8%)</td>
<td headers="stat_2" class="gt_row gt_center">13.0 (8.5%)</td>
<td headers="stat_3" class="gt_row gt_center">35.0 (12.6%)</td>
<td headers="stat_4" class="gt_row gt_center">47.0 (9.0%)</td>
<td headers="stat_5" class="gt_row gt_center">152.0 (11.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">16.0 (13.6%)</td>
<td headers="stat_2" class="gt_row gt_center">16.0 (10.5%)</td>
<td headers="stat_3" class="gt_row gt_center">38.0 (13.7%)</td>
<td headers="stat_4" class="gt_row gt_center">78.0 (15.0%)</td>
<td headers="stat_5" class="gt_row gt_center">199.0 (14.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">G3</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.239</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">48.0 (40.7%)</td>
<td headers="stat_2" class="gt_row gt_center">59.0 (38.6%)</td>
<td headers="stat_3" class="gt_row gt_center">118.0 (42.6%)</td>
<td headers="stat_4" class="gt_row gt_center">235.0 (45.1%)</td>
<td headers="stat_5" class="gt_row gt_center">631.0 (46.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">70.0 (59.3%)</td>
<td headers="stat_2" class="gt_row gt_center">94.0 (61.4%)</td>
<td headers="stat_3" class="gt_row gt_center">159.0 (57.4%)</td>
<td headers="stat_4" class="gt_row gt_center">286.0 (54.9%)</td>
<td headers="stat_5" class="gt_row gt_center">723.0 (53.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">G4</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">76.0 (64.4%)</td>
<td headers="stat_2" class="gt_row gt_center">88.0 (57.5%)</td>
<td headers="stat_3" class="gt_row gt_center">194.0 (70.0%)</td>
<td headers="stat_4" class="gt_row gt_center">353.0 (67.8%)</td>
<td headers="stat_5" class="gt_row gt_center">999.0 (73.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">42.0 (35.6%)</td>
<td headers="stat_2" class="gt_row gt_center">65.0 (42.5%)</td>
<td headers="stat_3" class="gt_row gt_center">83.0 (30.0%)</td>
<td headers="stat_4" class="gt_row gt_center">168.0 (32.2%)</td>
<td headers="stat_5" class="gt_row gt_center">355.0 (26.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
  </tbody>
  
  <tfoot class="gt_footnotes">
    <tr>
      <td class="gt_footnote" colspan="7"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span> n (%)</td>
    </tr>
    <tr>
      <td class="gt_footnote" colspan="7"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span> Pearson’s Chi-squared test</td>
    </tr>
  </tfoot>
</table>
</div>
</body>
</html>
