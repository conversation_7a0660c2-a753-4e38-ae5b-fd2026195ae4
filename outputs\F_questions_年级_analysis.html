<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<style>body{background-color:white;}</style>


</head>
<body>
<div id="zxumvudnsk" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;">
  <style>#zxumvudnsk table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#zxumvudnsk thead, #zxumvudnsk tbody, #zxumvudnsk tfoot, #zxumvudnsk tr, #zxumvudnsk td, #zxumvudnsk th {
  border-style: none;
}

#zxumvudnsk p {
  margin: 0;
  padding: 0;
}

#zxumvudnsk .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#zxumvudnsk .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#zxumvudnsk .gt_title {
  color: #333333;
  font-size: 125%;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#zxumvudnsk .gt_subtitle {
  color: #333333;
  font-size: 85%;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#zxumvudnsk .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#zxumvudnsk .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#zxumvudnsk .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#zxumvudnsk .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#zxumvudnsk .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#zxumvudnsk .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#zxumvudnsk .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#zxumvudnsk .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#zxumvudnsk .gt_spanner_row {
  border-bottom-style: hidden;
}

#zxumvudnsk .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#zxumvudnsk .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#zxumvudnsk .gt_from_md > :first-child {
  margin-top: 0;
}

#zxumvudnsk .gt_from_md > :last-child {
  margin-bottom: 0;
}

#zxumvudnsk .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#zxumvudnsk .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#zxumvudnsk .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#zxumvudnsk .gt_row_group_first td {
  border-top-width: 2px;
}

#zxumvudnsk .gt_row_group_first th {
  border-top-width: 2px;
}

#zxumvudnsk .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#zxumvudnsk .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#zxumvudnsk .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#zxumvudnsk .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#zxumvudnsk .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#zxumvudnsk .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#zxumvudnsk .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#zxumvudnsk .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#zxumvudnsk .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#zxumvudnsk .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#zxumvudnsk .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#zxumvudnsk .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#zxumvudnsk .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#zxumvudnsk .gt_left {
  text-align: left;
}

#zxumvudnsk .gt_center {
  text-align: center;
}

#zxumvudnsk .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#zxumvudnsk .gt_font_normal {
  font-weight: normal;
}

#zxumvudnsk .gt_font_bold {
  font-weight: bold;
}

#zxumvudnsk .gt_font_italic {
  font-style: italic;
}

#zxumvudnsk .gt_super {
  font-size: 65%;
}

#zxumvudnsk .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#zxumvudnsk .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#zxumvudnsk .gt_indent_1 {
  text-indent: 5px;
}

#zxumvudnsk .gt_indent_2 {
  text-indent: 10px;
}

#zxumvudnsk .gt_indent_3 {
  text-indent: 15px;
}

#zxumvudnsk .gt_indent_4 {
  text-indent: 20px;
}

#zxumvudnsk .gt_indent_5 {
  text-indent: 25px;
}
</style>
  <table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false">
  <thead>
    <tr class="gt_col_headings gt_spanner_row">
      <th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;题目&lt;/strong&gt;"><strong>题目</strong></th>
      <th class="gt_center gt_columns_top_border gt_column_spanner_outer" rowspan="1" colspan="3" scope="colgroup" id="&lt;strong&gt;年级&lt;/strong&gt;">
        <span class="gt_column_spanner"><strong>年级</strong></span>
      </th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;p-value&lt;/strong&gt;&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;2&lt;/sup&gt;&lt;/span&gt;"><strong>p-value</strong><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span></th>
    </tr>
    <tr class="gt_col_headings">
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;3年级&lt;/strong&gt;, N = 824&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>3年级</strong>, N = 824<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;4年级&lt;/strong&gt;, N = 831&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>4年级</strong>, N = 831<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;5年级&lt;/strong&gt;, N = 768&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>5年级</strong>, N = 768<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
    </tr>
  </thead>
  <tbody class="gt_table_body">
    <tr><td headers="label" class="gt_row gt_left">F1</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">760.0 (92.2%)</td>
<td headers="stat_2" class="gt_row gt_center">729.0 (87.7%)</td>
<td headers="stat_3" class="gt_row gt_center">651.0 (84.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">38.0 (4.6%)</td>
<td headers="stat_2" class="gt_row gt_center">56.0 (6.7%)</td>
<td headers="stat_3" class="gt_row gt_center">63.0 (8.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">14.0 (1.7%)</td>
<td headers="stat_2" class="gt_row gt_center">22.0 (2.6%)</td>
<td headers="stat_3" class="gt_row gt_center">39.0 (5.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">9.0 (1.1%)</td>
<td headers="stat_2" class="gt_row gt_center">16.0 (1.9%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (1.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">3.0 (0.4%)</td>
<td headers="stat_2" class="gt_row gt_center">8.0 (1.0%)</td>
<td headers="stat_3" class="gt_row gt_center">6.0 (0.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F2</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">446.0 (54.1%)</td>
<td headers="stat_2" class="gt_row gt_center">555.0 (66.8%)</td>
<td headers="stat_3" class="gt_row gt_center">513.0 (66.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">119.0 (14.4%)</td>
<td headers="stat_2" class="gt_row gt_center">108.0 (13.0%)</td>
<td headers="stat_3" class="gt_row gt_center">128.0 (16.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">135.0 (16.4%)</td>
<td headers="stat_2" class="gt_row gt_center">87.0 (10.5%)</td>
<td headers="stat_3" class="gt_row gt_center">70.0 (9.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">98.0 (11.9%)</td>
<td headers="stat_2" class="gt_row gt_center">67.0 (8.1%)</td>
<td headers="stat_3" class="gt_row gt_center">47.0 (6.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">26.0 (3.2%)</td>
<td headers="stat_2" class="gt_row gt_center">14.0 (1.7%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (1.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F3</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.006</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">499.0 (60.6%)</td>
<td headers="stat_2" class="gt_row gt_center">522.0 (62.8%)</td>
<td headers="stat_3" class="gt_row gt_center">513.0 (66.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">108.0 (13.1%)</td>
<td headers="stat_2" class="gt_row gt_center">119.0 (14.3%)</td>
<td headers="stat_3" class="gt_row gt_center">110.0 (14.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">106.0 (12.9%)</td>
<td headers="stat_2" class="gt_row gt_center">92.0 (11.1%)</td>
<td headers="stat_3" class="gt_row gt_center">89.0 (11.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">71.0 (8.6%)</td>
<td headers="stat_2" class="gt_row gt_center">66.0 (7.9%)</td>
<td headers="stat_3" class="gt_row gt_center">41.0 (5.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">40.0 (4.9%)</td>
<td headers="stat_2" class="gt_row gt_center">32.0 (3.9%)</td>
<td headers="stat_3" class="gt_row gt_center">14.0 (1.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F4</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.268</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">493.0 (59.8%)</td>
<td headers="stat_2" class="gt_row gt_center">470.0 (56.6%)</td>
<td headers="stat_3" class="gt_row gt_center">439.0 (57.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">131.0 (15.9%)</td>
<td headers="stat_2" class="gt_row gt_center">138.0 (16.6%)</td>
<td headers="stat_3" class="gt_row gt_center">132.0 (17.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">109.0 (13.2%)</td>
<td headers="stat_2" class="gt_row gt_center">120.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">129.0 (16.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">66.0 (8.0%)</td>
<td headers="stat_2" class="gt_row gt_center">76.0 (9.1%)</td>
<td headers="stat_3" class="gt_row gt_center">50.0 (6.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">25.0 (3.0%)</td>
<td headers="stat_2" class="gt_row gt_center">27.0 (3.2%)</td>
<td headers="stat_3" class="gt_row gt_center">17.0 (2.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F5</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.185</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">439.0 (53.3%)</td>
<td headers="stat_2" class="gt_row gt_center">426.0 (51.3%)</td>
<td headers="stat_3" class="gt_row gt_center">377.0 (49.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">120.0 (14.6%)</td>
<td headers="stat_2" class="gt_row gt_center">135.0 (16.2%)</td>
<td headers="stat_3" class="gt_row gt_center">132.0 (17.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">109.0 (13.2%)</td>
<td headers="stat_2" class="gt_row gt_center">132.0 (15.9%)</td>
<td headers="stat_3" class="gt_row gt_center">129.0 (16.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">102.0 (12.4%)</td>
<td headers="stat_2" class="gt_row gt_center">92.0 (11.1%)</td>
<td headers="stat_3" class="gt_row gt_center">96.0 (12.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">54.0 (6.6%)</td>
<td headers="stat_2" class="gt_row gt_center">46.0 (5.5%)</td>
<td headers="stat_3" class="gt_row gt_center">33.0 (4.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F6</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.010</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">130.0 (15.8%)</td>
<td headers="stat_2" class="gt_row gt_center">158.0 (19.0%)</td>
<td headers="stat_3" class="gt_row gt_center">182.0 (23.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">108.0 (13.1%)</td>
<td headers="stat_2" class="gt_row gt_center">107.0 (12.9%)</td>
<td headers="stat_3" class="gt_row gt_center">97.0 (12.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">203.0 (24.6%)</td>
<td headers="stat_2" class="gt_row gt_center">211.0 (25.4%)</td>
<td headers="stat_3" class="gt_row gt_center">183.0 (23.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">230.0 (27.9%)</td>
<td headers="stat_2" class="gt_row gt_center">216.0 (26.0%)</td>
<td headers="stat_3" class="gt_row gt_center">200.0 (26.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">153.0 (18.6%)</td>
<td headers="stat_2" class="gt_row gt_center">139.0 (16.7%)</td>
<td headers="stat_3" class="gt_row gt_center">105.0 (13.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F7</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.360</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">308.0 (37.4%)</td>
<td headers="stat_2" class="gt_row gt_center">283.0 (34.1%)</td>
<td headers="stat_3" class="gt_row gt_center">294.0 (38.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">137.0 (16.6%)</td>
<td headers="stat_2" class="gt_row gt_center">129.0 (15.5%)</td>
<td headers="stat_3" class="gt_row gt_center">129.0 (16.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">161.0 (19.5%)</td>
<td headers="stat_2" class="gt_row gt_center">171.0 (20.6%)</td>
<td headers="stat_3" class="gt_row gt_center">153.0 (19.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">142.0 (17.2%)</td>
<td headers="stat_2" class="gt_row gt_center">175.0 (21.1%)</td>
<td headers="stat_3" class="gt_row gt_center">125.0 (16.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">76.0 (9.2%)</td>
<td headers="stat_2" class="gt_row gt_center">73.0 (8.8%)</td>
<td headers="stat_3" class="gt_row gt_center">66.0 (8.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F8</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">37.0 (4.5%)</td>
<td headers="stat_2" class="gt_row gt_center">35.0 (4.2%)</td>
<td headers="stat_3" class="gt_row gt_center">52.0 (6.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">50.0 (6.1%)</td>
<td headers="stat_2" class="gt_row gt_center">66.0 (7.9%)</td>
<td headers="stat_3" class="gt_row gt_center">70.0 (9.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">75.0 (9.1%)</td>
<td headers="stat_2" class="gt_row gt_center">109.0 (13.1%)</td>
<td headers="stat_3" class="gt_row gt_center">125.0 (16.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">312.0 (37.9%)</td>
<td headers="stat_2" class="gt_row gt_center">350.0 (42.1%)</td>
<td headers="stat_3" class="gt_row gt_center">329.0 (42.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">350.0 (42.5%)</td>
<td headers="stat_2" class="gt_row gt_center">271.0 (32.6%)</td>
<td headers="stat_3" class="gt_row gt_center">191.0 (24.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F9</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.003</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">147.0 (17.8%)</td>
<td headers="stat_2" class="gt_row gt_center">202.0 (24.3%)</td>
<td headers="stat_3" class="gt_row gt_center">184.0 (24.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">147.0 (17.8%)</td>
<td headers="stat_2" class="gt_row gt_center">131.0 (15.8%)</td>
<td headers="stat_3" class="gt_row gt_center">159.0 (20.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">228.0 (27.7%)</td>
<td headers="stat_2" class="gt_row gt_center">201.0 (24.2%)</td>
<td headers="stat_3" class="gt_row gt_center">186.0 (24.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">200.0 (24.3%)</td>
<td headers="stat_2" class="gt_row gt_center">201.0 (24.2%)</td>
<td headers="stat_3" class="gt_row gt_center">170.0 (22.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">102.0 (12.4%)</td>
<td headers="stat_2" class="gt_row gt_center">96.0 (11.6%)</td>
<td headers="stat_3" class="gt_row gt_center">68.0 (8.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F10</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.031</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">99.0 (12.0%)</td>
<td headers="stat_2" class="gt_row gt_center">109.0 (13.1%)</td>
<td headers="stat_3" class="gt_row gt_center">123.0 (16.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">111.0 (13.5%)</td>
<td headers="stat_2" class="gt_row gt_center">112.0 (13.5%)</td>
<td headers="stat_3" class="gt_row gt_center">128.0 (16.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">209.0 (25.4%)</td>
<td headers="stat_2" class="gt_row gt_center">230.0 (27.7%)</td>
<td headers="stat_3" class="gt_row gt_center">205.0 (26.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">264.0 (32.0%)</td>
<td headers="stat_2" class="gt_row gt_center">258.0 (31.0%)</td>
<td headers="stat_3" class="gt_row gt_center">210.0 (27.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">141.0 (17.1%)</td>
<td headers="stat_2" class="gt_row gt_center">122.0 (14.7%)</td>
<td headers="stat_3" class="gt_row gt_center">101.0 (13.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F11</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.005</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">85.0 (10.3%)</td>
<td headers="stat_2" class="gt_row gt_center">101.0 (12.2%)</td>
<td headers="stat_3" class="gt_row gt_center">119.0 (15.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">101.0 (12.3%)</td>
<td headers="stat_2" class="gt_row gt_center">97.0 (11.7%)</td>
<td headers="stat_3" class="gt_row gt_center">104.0 (13.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">168.0 (20.4%)</td>
<td headers="stat_2" class="gt_row gt_center">172.0 (20.7%)</td>
<td headers="stat_3" class="gt_row gt_center">164.0 (21.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">244.0 (29.6%)</td>
<td headers="stat_2" class="gt_row gt_center">274.0 (33.0%)</td>
<td headers="stat_3" class="gt_row gt_center">227.0 (29.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">226.0 (27.4%)</td>
<td headers="stat_2" class="gt_row gt_center">187.0 (22.5%)</td>
<td headers="stat_3" class="gt_row gt_center">153.0 (19.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F12</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">18.0 (2.2%)</td>
<td headers="stat_2" class="gt_row gt_center">34.0 (4.1%)</td>
<td headers="stat_3" class="gt_row gt_center">51.0 (6.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">45.0 (5.5%)</td>
<td headers="stat_2" class="gt_row gt_center">52.0 (6.3%)</td>
<td headers="stat_3" class="gt_row gt_center">48.0 (6.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">80.0 (9.7%)</td>
<td headers="stat_2" class="gt_row gt_center">98.0 (11.8%)</td>
<td headers="stat_3" class="gt_row gt_center">105.0 (13.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">301.0 (36.5%)</td>
<td headers="stat_2" class="gt_row gt_center">318.0 (38.3%)</td>
<td headers="stat_3" class="gt_row gt_center">323.0 (42.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">380.0 (46.1%)</td>
<td headers="stat_2" class="gt_row gt_center">329.0 (39.6%)</td>
<td headers="stat_3" class="gt_row gt_center">240.0 (31.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F13</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">26.0 (3.2%)</td>
<td headers="stat_2" class="gt_row gt_center">34.0 (4.1%)</td>
<td headers="stat_3" class="gt_row gt_center">49.0 (6.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">46.0 (5.6%)</td>
<td headers="stat_2" class="gt_row gt_center">63.0 (7.6%)</td>
<td headers="stat_3" class="gt_row gt_center">64.0 (8.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">122.0 (14.8%)</td>
<td headers="stat_2" class="gt_row gt_center">110.0 (13.2%)</td>
<td headers="stat_3" class="gt_row gt_center">125.0 (16.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">282.0 (34.2%)</td>
<td headers="stat_2" class="gt_row gt_center">335.0 (40.3%)</td>
<td headers="stat_3" class="gt_row gt_center">309.0 (40.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">348.0 (42.2%)</td>
<td headers="stat_2" class="gt_row gt_center">289.0 (34.8%)</td>
<td headers="stat_3" class="gt_row gt_center">220.0 (28.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F14</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">32.0 (3.9%)</td>
<td headers="stat_2" class="gt_row gt_center">40.0 (4.8%)</td>
<td headers="stat_3" class="gt_row gt_center">47.0 (6.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">55.0 (6.7%)</td>
<td headers="stat_2" class="gt_row gt_center">54.0 (6.5%)</td>
<td headers="stat_3" class="gt_row gt_center">71.0 (9.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">99.0 (12.0%)</td>
<td headers="stat_2" class="gt_row gt_center">134.0 (16.1%)</td>
<td headers="stat_3" class="gt_row gt_center">135.0 (17.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">342.0 (41.5%)</td>
<td headers="stat_2" class="gt_row gt_center">325.0 (39.1%)</td>
<td headers="stat_3" class="gt_row gt_center">301.0 (39.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">296.0 (35.9%)</td>
<td headers="stat_2" class="gt_row gt_center">278.0 (33.5%)</td>
<td headers="stat_3" class="gt_row gt_center">213.0 (27.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
  </tbody>
  
  <tfoot class="gt_footnotes">
    <tr>
      <td class="gt_footnote" colspan="5"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span> n (%)</td>
    </tr>
    <tr>
      <td class="gt_footnote" colspan="5"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span> Pearson’s Chi-squared test</td>
    </tr>
  </tfoot>
</table>
</div>
</body>
</html>
