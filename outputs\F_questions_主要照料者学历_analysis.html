<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<style>body{background-color:white;}</style>


</head>
<body>
<div id="luqvujvied" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;">
  <style>#luqvujvied table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#luqvujvied thead, #luqvujvied tbody, #luqvujvied tfoot, #luqvujvied tr, #luqvujvied td, #luqvujvied th {
  border-style: none;
}

#luqvujvied p {
  margin: 0;
  padding: 0;
}

#luqvujvied .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#luqvujvied .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#luqvujvied .gt_title {
  color: #333333;
  font-size: 125%;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#luqvujvied .gt_subtitle {
  color: #333333;
  font-size: 85%;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#luqvujvied .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#luqvujvied .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#luqvujvied .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#luqvujvied .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#luqvujvied .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#luqvujvied .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#luqvujvied .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#luqvujvied .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#luqvujvied .gt_spanner_row {
  border-bottom-style: hidden;
}

#luqvujvied .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#luqvujvied .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#luqvujvied .gt_from_md > :first-child {
  margin-top: 0;
}

#luqvujvied .gt_from_md > :last-child {
  margin-bottom: 0;
}

#luqvujvied .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#luqvujvied .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#luqvujvied .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#luqvujvied .gt_row_group_first td {
  border-top-width: 2px;
}

#luqvujvied .gt_row_group_first th {
  border-top-width: 2px;
}

#luqvujvied .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#luqvujvied .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#luqvujvied .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#luqvujvied .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#luqvujvied .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#luqvujvied .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#luqvujvied .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#luqvujvied .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#luqvujvied .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#luqvujvied .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#luqvujvied .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#luqvujvied .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#luqvujvied .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#luqvujvied .gt_left {
  text-align: left;
}

#luqvujvied .gt_center {
  text-align: center;
}

#luqvujvied .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#luqvujvied .gt_font_normal {
  font-weight: normal;
}

#luqvujvied .gt_font_bold {
  font-weight: bold;
}

#luqvujvied .gt_font_italic {
  font-style: italic;
}

#luqvujvied .gt_super {
  font-size: 65%;
}

#luqvujvied .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#luqvujvied .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#luqvujvied .gt_indent_1 {
  text-indent: 5px;
}

#luqvujvied .gt_indent_2 {
  text-indent: 10px;
}

#luqvujvied .gt_indent_3 {
  text-indent: 15px;
}

#luqvujvied .gt_indent_4 {
  text-indent: 20px;
}

#luqvujvied .gt_indent_5 {
  text-indent: 25px;
}
</style>
  <table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false">
  <thead>
    <tr class="gt_col_headings gt_spanner_row">
      <th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;题目&lt;/strong&gt;"><strong>题目</strong></th>
      <th class="gt_center gt_columns_top_border gt_column_spanner_outer" rowspan="1" colspan="5" scope="colgroup" id="&lt;strong&gt;主要照料者学历&lt;/strong&gt;">
        <span class="gt_column_spanner"><strong>主要照料者学历</strong></span>
      </th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;p-value&lt;/strong&gt;&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;2&lt;/sup&gt;&lt;/span&gt;"><strong>p-value</strong><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span></th>
    </tr>
    <tr class="gt_col_headings">
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;小学及以下&lt;/strong&gt;, N = 118&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>小学及以下</strong>, N = 118<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;初中&lt;/strong&gt;, N = 153&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>初中</strong>, N = 153<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;高中&lt;/strong&gt;, N = 277&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>高中</strong>, N = 277<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;大专&lt;/strong&gt;, N = 521&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>大专</strong>, N = 521<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;本科及以上&lt;/strong&gt;, N = 1,354&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>本科及以上</strong>, N = 1,354<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
    </tr>
  </thead>
  <tbody class="gt_table_body">
    <tr><td headers="label" class="gt_row gt_left">F1</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.978</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">104.0 (88.1%)</td>
<td headers="stat_2" class="gt_row gt_center">135.0 (88.2%)</td>
<td headers="stat_3" class="gt_row gt_center">242.0 (87.4%)</td>
<td headers="stat_4" class="gt_row gt_center">461.0 (88.5%)</td>
<td headers="stat_5" class="gt_row gt_center">1,198.0 (88.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">8.0 (6.8%)</td>
<td headers="stat_2" class="gt_row gt_center">8.0 (5.2%)</td>
<td headers="stat_3" class="gt_row gt_center">21.0 (7.6%)</td>
<td headers="stat_4" class="gt_row gt_center">37.0 (7.1%)</td>
<td headers="stat_5" class="gt_row gt_center">83.0 (6.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">3.0 (2.5%)</td>
<td headers="stat_2" class="gt_row gt_center">7.0 (4.6%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (3.2%)</td>
<td headers="stat_4" class="gt_row gt_center">11.0 (2.1%)</td>
<td headers="stat_5" class="gt_row gt_center">45.0 (3.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">2.0 (1.7%)</td>
<td headers="stat_2" class="gt_row gt_center">1.0 (0.7%)</td>
<td headers="stat_3" class="gt_row gt_center">4.0 (1.4%)</td>
<td headers="stat_4" class="gt_row gt_center">8.0 (1.5%)</td>
<td headers="stat_5" class="gt_row gt_center">18.0 (1.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">1.0 (0.8%)</td>
<td headers="stat_2" class="gt_row gt_center">2.0 (1.3%)</td>
<td headers="stat_3" class="gt_row gt_center">1.0 (0.4%)</td>
<td headers="stat_4" class="gt_row gt_center">4.0 (0.8%)</td>
<td headers="stat_5" class="gt_row gt_center">9.0 (0.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F2</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">64.0 (54.2%)</td>
<td headers="stat_2" class="gt_row gt_center">88.0 (57.5%)</td>
<td headers="stat_3" class="gt_row gt_center">147.0 (53.1%)</td>
<td headers="stat_4" class="gt_row gt_center">322.0 (61.8%)</td>
<td headers="stat_5" class="gt_row gt_center">893.0 (66.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">19.0 (16.1%)</td>
<td headers="stat_2" class="gt_row gt_center">22.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">44.0 (15.9%)</td>
<td headers="stat_4" class="gt_row gt_center">80.0 (15.4%)</td>
<td headers="stat_5" class="gt_row gt_center">190.0 (14.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">15.0 (12.7%)</td>
<td headers="stat_2" class="gt_row gt_center">12.0 (7.8%)</td>
<td headers="stat_3" class="gt_row gt_center">54.0 (19.5%)</td>
<td headers="stat_4" class="gt_row gt_center">64.0 (12.3%)</td>
<td headers="stat_5" class="gt_row gt_center">147.0 (10.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">16.0 (13.6%)</td>
<td headers="stat_2" class="gt_row gt_center">27.0 (17.6%)</td>
<td headers="stat_3" class="gt_row gt_center">27.0 (9.7%)</td>
<td headers="stat_4" class="gt_row gt_center">42.0 (8.1%)</td>
<td headers="stat_5" class="gt_row gt_center">100.0 (7.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">4.0 (3.4%)</td>
<td headers="stat_2" class="gt_row gt_center">4.0 (2.6%)</td>
<td headers="stat_3" class="gt_row gt_center">5.0 (1.8%)</td>
<td headers="stat_4" class="gt_row gt_center">13.0 (2.5%)</td>
<td headers="stat_5" class="gt_row gt_center">23.0 (1.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F3</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">56.0 (47.5%)</td>
<td headers="stat_2" class="gt_row gt_center">95.0 (62.1%)</td>
<td headers="stat_3" class="gt_row gt_center">158.0 (57.0%)</td>
<td headers="stat_4" class="gt_row gt_center">331.0 (63.5%)</td>
<td headers="stat_5" class="gt_row gt_center">894.0 (66.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">19.0 (16.1%)</td>
<td headers="stat_2" class="gt_row gt_center">10.0 (6.5%)</td>
<td headers="stat_3" class="gt_row gt_center">44.0 (15.9%)</td>
<td headers="stat_4" class="gt_row gt_center">80.0 (15.4%)</td>
<td headers="stat_5" class="gt_row gt_center">184.0 (13.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">21.0 (17.8%)</td>
<td headers="stat_2" class="gt_row gt_center">22.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">30.0 (10.8%)</td>
<td headers="stat_4" class="gt_row gt_center">68.0 (13.1%)</td>
<td headers="stat_5" class="gt_row gt_center">146.0 (10.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">15.0 (12.7%)</td>
<td headers="stat_2" class="gt_row gt_center">17.0 (11.1%)</td>
<td headers="stat_3" class="gt_row gt_center">30.0 (10.8%)</td>
<td headers="stat_4" class="gt_row gt_center">33.0 (6.3%)</td>
<td headers="stat_5" class="gt_row gt_center">83.0 (6.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">7.0 (5.9%)</td>
<td headers="stat_2" class="gt_row gt_center">9.0 (5.9%)</td>
<td headers="stat_3" class="gt_row gt_center">15.0 (5.4%)</td>
<td headers="stat_4" class="gt_row gt_center">9.0 (1.7%)</td>
<td headers="stat_5" class="gt_row gt_center">46.0 (3.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F4</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">71.0 (60.2%)</td>
<td headers="stat_2" class="gt_row gt_center">73.0 (47.7%)</td>
<td headers="stat_3" class="gt_row gt_center">131.0 (47.3%)</td>
<td headers="stat_4" class="gt_row gt_center">300.0 (57.6%)</td>
<td headers="stat_5" class="gt_row gt_center">827.0 (61.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">15.0 (12.7%)</td>
<td headers="stat_2" class="gt_row gt_center">25.0 (16.3%)</td>
<td headers="stat_3" class="gt_row gt_center">53.0 (19.1%)</td>
<td headers="stat_4" class="gt_row gt_center">95.0 (18.2%)</td>
<td headers="stat_5" class="gt_row gt_center">213.0 (15.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">11.0 (9.3%)</td>
<td headers="stat_2" class="gt_row gt_center">26.0 (17.0%)</td>
<td headers="stat_3" class="gt_row gt_center">50.0 (18.1%)</td>
<td headers="stat_4" class="gt_row gt_center">78.0 (15.0%)</td>
<td headers="stat_5" class="gt_row gt_center">193.0 (14.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">16.0 (13.6%)</td>
<td headers="stat_2" class="gt_row gt_center">22.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">35.0 (12.6%)</td>
<td headers="stat_4" class="gt_row gt_center">35.0 (6.7%)</td>
<td headers="stat_5" class="gt_row gt_center">84.0 (6.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">5.0 (4.2%)</td>
<td headers="stat_2" class="gt_row gt_center">7.0 (4.6%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (2.9%)</td>
<td headers="stat_4" class="gt_row gt_center">13.0 (2.5%)</td>
<td headers="stat_5" class="gt_row gt_center">36.0 (2.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F5</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">53.0 (44.9%)</td>
<td headers="stat_2" class="gt_row gt_center">62.0 (40.5%)</td>
<td headers="stat_3" class="gt_row gt_center">152.0 (54.9%)</td>
<td headers="stat_4" class="gt_row gt_center">249.0 (47.8%)</td>
<td headers="stat_5" class="gt_row gt_center">726.0 (53.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">22.0 (18.6%)</td>
<td headers="stat_2" class="gt_row gt_center">27.0 (17.6%)</td>
<td headers="stat_3" class="gt_row gt_center">29.0 (10.5%)</td>
<td headers="stat_4" class="gt_row gt_center">80.0 (15.4%)</td>
<td headers="stat_5" class="gt_row gt_center">229.0 (16.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">17.0 (14.4%)</td>
<td headers="stat_2" class="gt_row gt_center">27.0 (17.6%)</td>
<td headers="stat_3" class="gt_row gt_center">50.0 (18.1%)</td>
<td headers="stat_4" class="gt_row gt_center">84.0 (16.1%)</td>
<td headers="stat_5" class="gt_row gt_center">192.0 (14.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">18.0 (15.3%)</td>
<td headers="stat_2" class="gt_row gt_center">24.0 (15.7%)</td>
<td headers="stat_3" class="gt_row gt_center">40.0 (14.4%)</td>
<td headers="stat_4" class="gt_row gt_center">68.0 (13.1%)</td>
<td headers="stat_5" class="gt_row gt_center">140.0 (10.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">8.0 (6.8%)</td>
<td headers="stat_2" class="gt_row gt_center">13.0 (8.5%)</td>
<td headers="stat_3" class="gt_row gt_center">6.0 (2.2%)</td>
<td headers="stat_4" class="gt_row gt_center">40.0 (7.7%)</td>
<td headers="stat_5" class="gt_row gt_center">66.0 (4.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F6</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.480</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">31.0 (26.3%)</td>
<td headers="stat_2" class="gt_row gt_center">26.0 (17.0%)</td>
<td headers="stat_3" class="gt_row gt_center">61.0 (22.0%)</td>
<td headers="stat_4" class="gt_row gt_center">89.0 (17.1%)</td>
<td headers="stat_5" class="gt_row gt_center">263.0 (19.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">13.0 (11.0%)</td>
<td headers="stat_2" class="gt_row gt_center">19.0 (12.4%)</td>
<td headers="stat_3" class="gt_row gt_center">34.0 (12.3%)</td>
<td headers="stat_4" class="gt_row gt_center">70.0 (13.4%)</td>
<td headers="stat_5" class="gt_row gt_center">176.0 (13.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">28.0 (23.7%)</td>
<td headers="stat_2" class="gt_row gt_center">36.0 (23.5%)</td>
<td headers="stat_3" class="gt_row gt_center">77.0 (27.8%)</td>
<td headers="stat_4" class="gt_row gt_center">120.0 (23.0%)</td>
<td headers="stat_5" class="gt_row gt_center">336.0 (24.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">25.0 (21.2%)</td>
<td headers="stat_2" class="gt_row gt_center">40.0 (26.1%)</td>
<td headers="stat_3" class="gt_row gt_center">70.0 (25.3%)</td>
<td headers="stat_4" class="gt_row gt_center">152.0 (29.2%)</td>
<td headers="stat_5" class="gt_row gt_center">359.0 (26.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">21.0 (17.8%)</td>
<td headers="stat_2" class="gt_row gt_center">32.0 (20.9%)</td>
<td headers="stat_3" class="gt_row gt_center">35.0 (12.6%)</td>
<td headers="stat_4" class="gt_row gt_center">90.0 (17.3%)</td>
<td headers="stat_5" class="gt_row gt_center">219.0 (16.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F7</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">36.0 (30.5%)</td>
<td headers="stat_2" class="gt_row gt_center">48.0 (31.4%)</td>
<td headers="stat_3" class="gt_row gt_center">87.0 (31.4%)</td>
<td headers="stat_4" class="gt_row gt_center">181.0 (34.7%)</td>
<td headers="stat_5" class="gt_row gt_center">533.0 (39.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">12.0 (10.2%)</td>
<td headers="stat_2" class="gt_row gt_center">20.0 (13.1%)</td>
<td headers="stat_3" class="gt_row gt_center">42.0 (15.2%)</td>
<td headers="stat_4" class="gt_row gt_center">87.0 (16.7%)</td>
<td headers="stat_5" class="gt_row gt_center">234.0 (17.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">20.0 (16.9%)</td>
<td headers="stat_2" class="gt_row gt_center">26.0 (17.0%)</td>
<td headers="stat_3" class="gt_row gt_center">61.0 (22.0%)</td>
<td headers="stat_4" class="gt_row gt_center">113.0 (21.7%)</td>
<td headers="stat_5" class="gt_row gt_center">265.0 (19.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">35.0 (29.7%)</td>
<td headers="stat_2" class="gt_row gt_center">42.0 (27.5%)</td>
<td headers="stat_3" class="gt_row gt_center">59.0 (21.3%)</td>
<td headers="stat_4" class="gt_row gt_center">95.0 (18.2%)</td>
<td headers="stat_5" class="gt_row gt_center">211.0 (15.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">15.0 (12.7%)</td>
<td headers="stat_2" class="gt_row gt_center">17.0 (11.1%)</td>
<td headers="stat_3" class="gt_row gt_center">28.0 (10.1%)</td>
<td headers="stat_4" class="gt_row gt_center">45.0 (8.6%)</td>
<td headers="stat_5" class="gt_row gt_center">110.0 (8.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F8</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.051</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">11.0 (9.3%)</td>
<td headers="stat_2" class="gt_row gt_center">9.0 (5.9%)</td>
<td headers="stat_3" class="gt_row gt_center">15.0 (5.4%)</td>
<td headers="stat_4" class="gt_row gt_center">24.0 (4.6%)</td>
<td headers="stat_5" class="gt_row gt_center">65.0 (4.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">10.0 (8.5%)</td>
<td headers="stat_2" class="gt_row gt_center">12.0 (7.8%)</td>
<td headers="stat_3" class="gt_row gt_center">30.0 (10.8%)</td>
<td headers="stat_4" class="gt_row gt_center">34.0 (6.5%)</td>
<td headers="stat_5" class="gt_row gt_center">100.0 (7.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">9.0 (7.6%)</td>
<td headers="stat_2" class="gt_row gt_center">16.0 (10.5%)</td>
<td headers="stat_3" class="gt_row gt_center">35.0 (12.6%)</td>
<td headers="stat_4" class="gt_row gt_center">69.0 (13.2%)</td>
<td headers="stat_5" class="gt_row gt_center">180.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">44.0 (37.3%)</td>
<td headers="stat_2" class="gt_row gt_center">62.0 (40.5%)</td>
<td headers="stat_3" class="gt_row gt_center">129.0 (46.6%)</td>
<td headers="stat_4" class="gt_row gt_center">226.0 (43.4%)</td>
<td headers="stat_5" class="gt_row gt_center">530.0 (39.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">44.0 (37.3%)</td>
<td headers="stat_2" class="gt_row gt_center">54.0 (35.3%)</td>
<td headers="stat_3" class="gt_row gt_center">68.0 (24.5%)</td>
<td headers="stat_4" class="gt_row gt_center">168.0 (32.2%)</td>
<td headers="stat_5" class="gt_row gt_center">478.0 (35.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F9</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.484</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">33.0 (28.0%)</td>
<td headers="stat_2" class="gt_row gt_center">31.0 (20.3%)</td>
<td headers="stat_3" class="gt_row gt_center">52.0 (18.8%)</td>
<td headers="stat_4" class="gt_row gt_center">118.0 (22.6%)</td>
<td headers="stat_5" class="gt_row gt_center">299.0 (22.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">22.0 (18.6%)</td>
<td headers="stat_2" class="gt_row gt_center">27.0 (17.6%)</td>
<td headers="stat_3" class="gt_row gt_center">55.0 (19.9%)</td>
<td headers="stat_4" class="gt_row gt_center">94.0 (18.0%)</td>
<td headers="stat_5" class="gt_row gt_center">239.0 (17.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">21.0 (17.8%)</td>
<td headers="stat_2" class="gt_row gt_center">33.0 (21.6%)</td>
<td headers="stat_3" class="gt_row gt_center">67.0 (24.2%)</td>
<td headers="stat_4" class="gt_row gt_center">140.0 (26.9%)</td>
<td headers="stat_5" class="gt_row gt_center">354.0 (26.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">26.0 (22.0%)</td>
<td headers="stat_2" class="gt_row gt_center">41.0 (26.8%)</td>
<td headers="stat_3" class="gt_row gt_center">73.0 (26.4%)</td>
<td headers="stat_4" class="gt_row gt_center">107.0 (20.5%)</td>
<td headers="stat_5" class="gt_row gt_center">324.0 (23.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">16.0 (13.6%)</td>
<td headers="stat_2" class="gt_row gt_center">21.0 (13.7%)</td>
<td headers="stat_3" class="gt_row gt_center">30.0 (10.8%)</td>
<td headers="stat_4" class="gt_row gt_center">62.0 (11.9%)</td>
<td headers="stat_5" class="gt_row gt_center">137.0 (10.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F10</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.742</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">22.0 (18.6%)</td>
<td headers="stat_2" class="gt_row gt_center">22.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">35.0 (12.6%)</td>
<td headers="stat_4" class="gt_row gt_center">72.0 (13.8%)</td>
<td headers="stat_5" class="gt_row gt_center">180.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">14.0 (11.9%)</td>
<td headers="stat_2" class="gt_row gt_center">22.0 (14.4%)</td>
<td headers="stat_3" class="gt_row gt_center">50.0 (18.1%)</td>
<td headers="stat_4" class="gt_row gt_center">80.0 (15.4%)</td>
<td headers="stat_5" class="gt_row gt_center">185.0 (13.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">26.0 (22.0%)</td>
<td headers="stat_2" class="gt_row gt_center">44.0 (28.8%)</td>
<td headers="stat_3" class="gt_row gt_center">63.0 (22.7%)</td>
<td headers="stat_4" class="gt_row gt_center">137.0 (26.3%)</td>
<td headers="stat_5" class="gt_row gt_center">374.0 (27.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">40.0 (33.9%)</td>
<td headers="stat_2" class="gt_row gt_center">47.0 (30.7%)</td>
<td headers="stat_3" class="gt_row gt_center">87.0 (31.4%)</td>
<td headers="stat_4" class="gt_row gt_center">152.0 (29.2%)</td>
<td headers="stat_5" class="gt_row gt_center">406.0 (30.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">16.0 (13.6%)</td>
<td headers="stat_2" class="gt_row gt_center">18.0 (11.8%)</td>
<td headers="stat_3" class="gt_row gt_center">42.0 (15.2%)</td>
<td headers="stat_4" class="gt_row gt_center">80.0 (15.4%)</td>
<td headers="stat_5" class="gt_row gt_center">208.0 (15.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F11</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.111</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">26.0 (22.0%)</td>
<td headers="stat_2" class="gt_row gt_center">19.0 (12.4%)</td>
<td headers="stat_3" class="gt_row gt_center">28.0 (10.1%)</td>
<td headers="stat_4" class="gt_row gt_center">66.0 (12.7%)</td>
<td headers="stat_5" class="gt_row gt_center">166.0 (12.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">15.0 (12.7%)</td>
<td headers="stat_2" class="gt_row gt_center">15.0 (9.8%)</td>
<td headers="stat_3" class="gt_row gt_center">38.0 (13.7%)</td>
<td headers="stat_4" class="gt_row gt_center">65.0 (12.5%)</td>
<td headers="stat_5" class="gt_row gt_center">169.0 (12.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">22.0 (18.6%)</td>
<td headers="stat_2" class="gt_row gt_center">23.0 (15.0%)</td>
<td headers="stat_3" class="gt_row gt_center">53.0 (19.1%)</td>
<td headers="stat_4" class="gt_row gt_center">109.0 (20.9%)</td>
<td headers="stat_5" class="gt_row gt_center">297.0 (22.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">37.0 (31.4%)</td>
<td headers="stat_2" class="gt_row gt_center">56.0 (36.6%)</td>
<td headers="stat_3" class="gt_row gt_center">96.0 (34.7%)</td>
<td headers="stat_4" class="gt_row gt_center">153.0 (29.4%)</td>
<td headers="stat_5" class="gt_row gt_center">403.0 (29.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">18.0 (15.3%)</td>
<td headers="stat_2" class="gt_row gt_center">40.0 (26.1%)</td>
<td headers="stat_3" class="gt_row gt_center">62.0 (22.4%)</td>
<td headers="stat_4" class="gt_row gt_center">128.0 (24.6%)</td>
<td headers="stat_5" class="gt_row gt_center">318.0 (23.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F12</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.082</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">8.0 (6.8%)</td>
<td headers="stat_2" class="gt_row gt_center">12.0 (7.8%)</td>
<td headers="stat_3" class="gt_row gt_center">12.0 (4.3%)</td>
<td headers="stat_4" class="gt_row gt_center">20.0 (3.8%)</td>
<td headers="stat_5" class="gt_row gt_center">51.0 (3.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">5.0 (4.2%)</td>
<td headers="stat_2" class="gt_row gt_center">6.0 (3.9%)</td>
<td headers="stat_3" class="gt_row gt_center">30.0 (10.8%)</td>
<td headers="stat_4" class="gt_row gt_center">33.0 (6.3%)</td>
<td headers="stat_5" class="gt_row gt_center">71.0 (5.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">15.0 (12.7%)</td>
<td headers="stat_2" class="gt_row gt_center">20.0 (13.1%)</td>
<td headers="stat_3" class="gt_row gt_center">28.0 (10.1%)</td>
<td headers="stat_4" class="gt_row gt_center">63.0 (12.1%)</td>
<td headers="stat_5" class="gt_row gt_center">157.0 (11.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">45.0 (38.1%)</td>
<td headers="stat_2" class="gt_row gt_center">59.0 (38.6%)</td>
<td headers="stat_3" class="gt_row gt_center">105.0 (37.9%)</td>
<td headers="stat_4" class="gt_row gt_center">209.0 (40.1%)</td>
<td headers="stat_5" class="gt_row gt_center">524.0 (38.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">45.0 (38.1%)</td>
<td headers="stat_2" class="gt_row gt_center">56.0 (36.6%)</td>
<td headers="stat_3" class="gt_row gt_center">102.0 (36.8%)</td>
<td headers="stat_4" class="gt_row gt_center">196.0 (37.6%)</td>
<td headers="stat_5" class="gt_row gt_center">550.0 (40.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F13</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.091</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">7.0 (5.9%)</td>
<td headers="stat_2" class="gt_row gt_center">9.0 (5.9%)</td>
<td headers="stat_3" class="gt_row gt_center">9.0 (3.2%)</td>
<td headers="stat_4" class="gt_row gt_center">25.0 (4.8%)</td>
<td headers="stat_5" class="gt_row gt_center">59.0 (4.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">10.0 (8.5%)</td>
<td headers="stat_2" class="gt_row gt_center">8.0 (5.2%)</td>
<td headers="stat_3" class="gt_row gt_center">27.0 (9.7%)</td>
<td headers="stat_4" class="gt_row gt_center">38.0 (7.3%)</td>
<td headers="stat_5" class="gt_row gt_center">90.0 (6.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">16.0 (13.6%)</td>
<td headers="stat_2" class="gt_row gt_center">20.0 (13.1%)</td>
<td headers="stat_3" class="gt_row gt_center">57.0 (20.6%)</td>
<td headers="stat_4" class="gt_row gt_center">65.0 (12.5%)</td>
<td headers="stat_5" class="gt_row gt_center">199.0 (14.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">43.0 (36.4%)</td>
<td headers="stat_2" class="gt_row gt_center">68.0 (44.4%)</td>
<td headers="stat_3" class="gt_row gt_center">107.0 (38.6%)</td>
<td headers="stat_4" class="gt_row gt_center">206.0 (39.5%)</td>
<td headers="stat_5" class="gt_row gt_center">502.0 (37.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">42.0 (35.6%)</td>
<td headers="stat_2" class="gt_row gt_center">48.0 (31.4%)</td>
<td headers="stat_3" class="gt_row gt_center">77.0 (27.8%)</td>
<td headers="stat_4" class="gt_row gt_center">187.0 (35.9%)</td>
<td headers="stat_5" class="gt_row gt_center">503.0 (37.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F14</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="stat_4" class="gt_row gt_center"><br /></td>
<td headers="stat_5" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.620</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">10.0 (8.5%)</td>
<td headers="stat_2" class="gt_row gt_center">7.0 (4.6%)</td>
<td headers="stat_3" class="gt_row gt_center">7.0 (2.5%)</td>
<td headers="stat_4" class="gt_row gt_center">27.0 (5.2%)</td>
<td headers="stat_5" class="gt_row gt_center">68.0 (5.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">4.0 (3.4%)</td>
<td headers="stat_2" class="gt_row gt_center">14.0 (9.2%)</td>
<td headers="stat_3" class="gt_row gt_center">20.0 (7.2%)</td>
<td headers="stat_4" class="gt_row gt_center">38.0 (7.3%)</td>
<td headers="stat_5" class="gt_row gt_center">104.0 (7.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">19.0 (16.1%)</td>
<td headers="stat_2" class="gt_row gt_center">24.0 (15.7%)</td>
<td headers="stat_3" class="gt_row gt_center">39.0 (14.1%)</td>
<td headers="stat_4" class="gt_row gt_center">77.0 (14.8%)</td>
<td headers="stat_5" class="gt_row gt_center">209.0 (15.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">45.0 (38.1%)</td>
<td headers="stat_2" class="gt_row gt_center">54.0 (35.3%)</td>
<td headers="stat_3" class="gt_row gt_center">123.0 (44.4%)</td>
<td headers="stat_4" class="gt_row gt_center">216.0 (41.5%)</td>
<td headers="stat_5" class="gt_row gt_center">530.0 (39.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">40.0 (33.9%)</td>
<td headers="stat_2" class="gt_row gt_center">54.0 (35.3%)</td>
<td headers="stat_3" class="gt_row gt_center">88.0 (31.8%)</td>
<td headers="stat_4" class="gt_row gt_center">163.0 (31.3%)</td>
<td headers="stat_5" class="gt_row gt_center">442.0 (32.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
  </tbody>
  
  <tfoot class="gt_footnotes">
    <tr>
      <td class="gt_footnote" colspan="7"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span> n (%)</td>
    </tr>
    <tr>
      <td class="gt_footnote" colspan="7"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span> Pearson’s Chi-squared test</td>
    </tr>
  </tfoot>
</table>
</div>
</body>
</html>
