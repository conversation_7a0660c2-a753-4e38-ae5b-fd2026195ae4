# Part one: data combine ----------------------------------------------------------------------

# 加载必要的库
library(readxl)
# library(dplyr)
# library(purrr)
# library(stringr)
library(tidyverse)
library(gtsummary)

# 1. 合并学生营养数据、家长营养数据及学生BMI数据
source("data_clean_kid.R")
source("data_clean_parents.R")
source("data_clean_BMI.R")

kid_data <- read.csv("outputs/processed_data.csv")
bmi_data <- read.csv("outputs/processed_data_bmi.csv")
parent_data <- read.csv("outputs/processed_data_parents.csv")

# 合并学生营养数据与BMI数据
data <- kid_data %>%
    left_join(bmi_data,
        by = c("学校名称", "年级", "班级", "学生姓名"),
        suffix = c("", "_bmi")
    )  %>%
    # 合并家长营养数据
    left_join(
        parent_data,
        by = c(
            "学校名称" = "school",
            "年级" = "Kidgrade",
            "学生姓名" = "Kidname"
        ),
        suffix = c("", "_parent")
    ) %>%
    # 移除可能重复的列
    select(-ends_with("_bmi"), -ends_with(".y"))

cat("数据合并完成！\n")



# 2. 计算BMI并判断超重肥胖
cat("正在计算BMI并判断超重肥胖...\n")

# 读取BMI判断标准
bmi_standard <- readxl::read_excel("BMI判断标准.xls")

# 处理身高体重单位并计算BMI
all_data <- data %>%
    mutate(
        # 转换身高单位（假设身高>2的单位是厘米，否则是米）
        身高 = ifelse(身高 > 2, 身高 / 100, 身高),
        # 转换体重单位（假设体重>=200的单位是斤，否则是公斤）
        体重 = ifelse(体重 >= 200, 体重 / 2, 体重),
        BMI = round(体重 / (身高^2), 1)
    ) %>%
    left_join(bmi_standard, by = c("性别", "年级")) %>%
    mutate(
        healthy = if_else(BMI < 消瘦标准, "消瘦", 
                          ifelse(BMI >= 超重肥胖标准, "超重肥胖","正常"), 
                          missing = "无法判断")
        # thinness = if_else(BMI < 消瘦标准, "是", "否", missing = "无法判断"),
        # obesity = if_else(BMI >= 超重肥胖标准, "是", "否", missing = "无法判断")
    )

all_data <- all_data %>%
    mutate(
      年级 = case_match(
        年级,
        3 ~ "3年级",
        4 ~ "4年级",
        5 ~ "5年级",
        NA ~ as.character(年级)
      ),
      性别 = case_match(
        性别,
        1 ~ "男",
        2 ~ "女",
        NA ~ NA
      ),
      主要照料者 = case_match(
        A1,
        1 ~ "父母",
        2 ~ "祖辈",
        3 ~ "其他",
        NA ~ NA
      ),
      主要照料者学历 = case_match(
        A2,
        1 ~ "小学及以下",
        2 ~ "初中",
        3 ~ "高中",
        4 ~ "大专",
        5 ~ "本科及以上",
        NA ~ NA
      ),
      家庭经济条件 = case_when(
        A3 == 2  ~ "低",
        A3 == 1 & A4 <=2 ~ "中",
        A3 == 1 & A4 >2 ~ "高",
        NA ~ NA
      )
    )
# 展示无法判断肥胖状态的记录
# cat("发现", sum(all_data$obesity == "无法判断"), "条无法判断肥胖状态的记录：\n")
# non_obese <- dt %>%
#     filter(obesity == "无法判断") %>%
#     select(学校名称, 年级, 班级, 学生姓名, 性别, 身高, 体重, BMI) %>%
#     view()

# 保存处理后的数据
cat("保存处理后的数据...\n")
write.csv(all_data, "outputs/cleaned_all_data.csv", row.names = FALSE)

cat("数据清洗完成！\n")


# Part two: data analysis ----------------------------------------------------------------------

# 1.描述性统计分析
# 1.1 首先判断cols_to_convert中的包含的列数据分布的情况，如果为正态，则采用平均数报告，否则用中位数报告
cols_to_convert <- c("Knowledge_Concepts", "Food_Selection", "Food_Preparation",
                    "Food_Consumption", "Domain_Cognition", "Domain_Skills",
                    "Total_Core_Information")

stats_results <- data.frame(
  Variable = character(),
  Distribution = character(),
  Mean = numeric(),
  Median = numeric(),
  SD = numeric(),
  p25 = numeric(),
  p75 = numeric(),
  IQR = numeric(),
  Recommended_Statistic = character(),
  stringsAsFactors = FALSE
)


for (col_name in cols_to_convert) {

  # 执行Shapiro-Wilk正态性检验
  shapiro_test <- tryCatch({
    shapiro.test(all_data[[col_name]])
  }, error = function(e) {
    cat(sprintf("警告: %s 无法进行Shapiro-Wilk检验: %s\n", col_name, e$message))
    return(list(p.value = 0)) # 默认不是正态分布
  })
  
  # 判断是否为正态分布 (p > 0.05表示可能服从正态分布)
  is_normal <- shapiro_test$p.value > 0.05
  
  # 计算基本统计量
  mean_val <- mean(all_data[[col_name]], na.rm = TRUE)
  median_val <- median(all_data[[col_name]], na.rm = TRUE)
  sd_val <- sd(all_data[[col_name]], na.rm = TRUE)
  p25_val <- quantile(all_data[[col_name]], 0.25, na.rm = TRUE)
  p75_val <- quantile(all_data[[col_name]], 0.75, na.rm = TRUE)
  iqr_val <- IQR(all_data[[col_name]], na.rm = TRUE)
  
  # 确定推荐使用的统计量
  recommended <- ifelse(is_normal, "均值", "中位数")
  
  # 添加到结果数据框
  stats_results <- rbind(stats_results, data.frame(
    Variable = col_name,
    Distribution = ifelse(is_normal, "正态分布", "非正态分布"),
    Mean = mean_val,
    Median = median_val,
    SD = sd_val,
    p25 = p25_val,
    p75 = p75_val,
    IQR = iqr_val,
    Recommended_Statistic = recommended,
    stringsAsFactors = FALSE
  ))
  
  cat(sprintf("%s: %s (p=%f), 推荐使用%s\n", 
              col_name, 
              ifelse(is_normal, "服从正态分布", "不服从正态分布"),
              shapiro_test$p.value,
              recommended))
}


# 创建整体得分分布图
stats_plot <- stats_results %>%
  select(Variable, Median, p25, p75) %>%
  filter(Variable %in% c("Food_Selection", 
                        "Food_Preparation", 
                        "Food_Consumption",
                        "Domain_Cognition", 
                        "Total_Core_Information")) %>%
  mutate(
    Variable = factor(Variable, 
                     levels = c("Food_Selection", 
                              "Food_Preparation", 
                              "Food_Consumption",
                              "Domain_Cognition", 
                              "Total_Core_Information"),
                     labels = c("营养相关知识理念", 
                                "选择食物",
                                "制作食物", 
                                "摄入食物",
                                "营养素养总分"))) %>%
  ggplot(aes(x = Variable, y = Median)) +
  geom_col(fill = "#3498db", alpha = 0.7, width = 0.6) +
  geom_errorbar(aes(ymin = p25, ymax = p75),
                width = 0.2, color = "#2c3e50") +
  labs(title = "营养素养各维度得分",
       x = "维度",
       y = "得分") +
  theme_classic() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    plot.title = element_text(hjust = 0.5, face = "bold")
  )

# 保存图表
ggsave("outputs/dimension_scores_distribution.png", 
       stats_plot, 
       width = 10, 
       height = 6, 
       dpi = 300)

cat("维度得分分布图已保存至 outputs/dimension_scores_distribution.png\n")


# 1.2 按照年级、性别、主要照料者类别进行分组，计算营养素养各维度及总分数的中位数和四分位间距（IQR） 
# cat("\n正在计算分组统计量...\n")

# # 定义需要统计的列（保持与cols_to_convert一致）
stats_columns <- cols_to_convert

# 按年级、性别和主要照料者类别分组计算统计量
# # 创建统计函数
# calculate_group_stats <- function(data, group_vars) {
#   results_list <- list()
  
#   for(group_var in group_vars) {
#     # 基础统计量计算
#     base_stats <- data %>%
#       group_by(!!sym(group_var)) %>%
#       summarise(
#         across(all_of(stats_columns),
#                ~paste0(median(., na.rm = TRUE), 
#                       "(", round(quantile(., 0.25, na.rm = TRUE), 1), 
#                       "-", round(quantile(., 0.75, na.rm = TRUE), 1), ")"),
#                .names = "{col}_median_IQR"
#         ),
#         .groups = "drop"
#       )
    
#     # 非参检验
#     test_stats <- map_dfr(stats_columns, function(col) {
#       if(length(unique(data[[group_var]])) > 2) {
#         test_result <- kruskal.test(data[[col]] ~ data[[group_var]])
#         data.frame(
#           维度 = col,
#           检验方法 = "Kruskal-Wallis H",
#           统计量 = round(test_result$statistic, 2),
#           p值 = round(test_result$p.value, 3)
#         )
#       } else {
#         test_result <- wilcox.test(data[[col]] ~ data[[group_var]])
#         data.frame(
#           维度 = col,
#           检验方法 = "Mann-Whitney U",
#           统计量 = round(test_result$statistic, 2),
#           p值 = round(test_result$p.value, 3)
#         )
#       }
#     })
    
#     results_list[[group_var]] <- list(
#       base_stats = base_stats,
#       test_stats = test_stats
#     )
#   }
  
#   return(results_list)
# }

# # 定义分组变量
# group_vars <- c("年级", "性别", "主要照料者", "主要照料者学历", "家庭经济条件")

# # 使用函数
# group_stats <- calculate_group_stats(all_data, group_vars)

# # 创建空的数据框来存储结果
# all_base_stats <- data.frame()
# all_test_stats <- data.frame()

# # 打印结果并存储
# for(group_var in group_vars) {
#   cat("\n", "=====", group_var, "的统计结果", "=====\n")
  
#   # 基础统计量
#   cat("\n基础统计量：\n")
#   base_stats_df <- group_stats[[group_var]]$base_stats %>%
#     mutate(分组变量 = group_var) %>%
#     relocate(分组变量)  # 将分组变量移到第一列
#   print(base_stats_df)
#   all_base_stats <- bind_rows(all_base_stats, base_stats_df)
  
#   # 检验结果
#   cat("\n检验结果：\n")
#   test_stats_df <- group_stats[[group_var]]$test_stats %>%
#     mutate(分组变量 = group_var) %>%
#     relocate(分组变量)  # 将分组变量移到第一列
#   print(test_stats_df)
#   all_test_stats <- bind_rows(all_test_stats, test_stats_df)
# }

# # 保存结果到CSV文件
# write.csv(all_base_stats, "outputs/group_base_statistics.csv", row.names = FALSE)
# write.csv(all_test_stats, "outputs/group_test_statistics.csv", row.names = FALSE)

# cat("\n统计结果已保存至 outputs/group_base_statistics.csv 和 outputs/group_test_statistics.csv\n")





# # 提取并打印test_stats表格
# test_stats_tables <- map(group_stats, ~.x$test_stats)
# names(test_stats_tables) <- group_vars

# # 打印每个分组的检验结果
# for(i in seq_along(test_stats_tables)) {
#   cat("\n", group_vars[i], "分组的检验结果:\n")
#   print(test_stats_tables[[i]])
# }

# # 1.2 使用gtsummary进行分组统计分析
# cat("\n正在使用gtsummary进行分组统计分析...\n")

# # 创建分组统计分析函数
# create_group_analysis <- function(data, group_var, stats_cols, 
#                                 title_prefix = "分组") {
#   # 创建分组统计表
#   stats_tbl <- data %>%
#     select(all_of(c(group_var, stats_cols))) %>%
#     tbl_summary(
#       by = group_var,
#       statistic = list(all_continuous() ~ "{median} ({p25}, {p75})"),
#       missing = "no"
#     ) %>%
#     add_stat_label() %>%
#     add_p(
#       test = list(all_continuous() ~ 
#                    ifelse(length(unique(data[[group_var]])) > 2, 
#                           "kruskal.test", "wilcox.test")),
#       pvalue_fun = ~ style_pvalue(.x, digits = 3)
#     ) %>%
#     modify_header(label = "**维度**") %>%
#     modify_spanning_header(all_stat_cols() ~ sprintf("**%s**", title_prefix))
  
#   # 执行非参数检验
#   test_results <- map_dfr(stats_cols, function(col) {
#     test_result <- if(length(unique(data[[group_var]])) > 2) {
#       kruskal.test(data[[col]] ~ data[[group_var]])
#     } else {
#       wilcox.test(data[[col]] ~ data[[group_var]])
#     }
    
#     data.frame(
#       维度 = col,
#       分组变量 = group_var,
#       统计量 = round(test_result$statistic, 2),
#       自由度 = if("parameter" %in% names(test_result)) test_result$parameter else NA,
#       p值 = round(test_result$p.value, 3)
#     )
#   })
  
#   return(list(table = stats_tbl, tests = test_results))
# }

# # 使用函数进行分组分析
# cat("\n正在使用gtsummary进行分组统计分析...\n")

# # 执行各分组的分析
# 年级_results <- create_group_analysis(all_data, "年级", stats_columns, "年级")
# 性别_results <- create_group_analysis(all_data, "性别", stats_columns,  "性别")
# 照料者_results <- create_group_analysis(all_data, "A1", stats_columns,  "主要照料者")

# # 合并所有统计表
# merged_stats <- tbl_merge(
#   tbls = list(年级_results$table, 性别_results$table, 照料者_results$table),
#   tab_spanner = c("**年级分组**", "**性别分组**", "**主要照料者分组**")
# )

# # 保存合并后的结果
# merged_stats %>%
#   as_gt() %>%
#   gt::gtsave(filename = "outputs/merged_statistics.html")

# merged_stats %>%
#   as_flex_table() %>%
#   flextable::save_as_docx(path = "outputs/merged_statistics.docx")

# # 合并所有检验结果并保存
# all_tests <- bind_rows(
#   年级_results$tests,
#   性别_results$tests,
#   照料者_results$tests
# )



# cat("分组统计结果已保存至 outputs/group_statistics.html 和 outputs/group_statistics.docx\n")

# 额外添加Kruskal-Wallis检验结果
# kw_results <- map_dfr(stats_columns, function(col) {
#   test_result <- kruskal.test(all_data[[col]] ~ interaction(all_data$年级, all_data$性别))
#   data.frame(
#     维度 = col,
#     统计量 = round(test_result$statistic, 2),
#     自由度 = test_result$parameter,
#     p值 = round(test_result$p.value, 3)
#   )
# })

# # 保存Kruskal-Wallis检验结果
# write.csv(all_tests , "outputs/kruskal_wallis_results_1.csv", row.names = FALSE)
# write.csv(kw_results, "outputs/kruskal_wallis_results.csv", row.names = FALSE)
# cat("Kruskal-Wallis检验结果已保存至 outputs/kruskal_wallis_results.csv\n")


# # 1.3 分组统计结果可视化
# cat("\n分组统计结果可视化...\n")
# library(ggplot2)

# # 转换数据为长格式
# viz_data <- all_data %>%
#   group_by(年级, 性别) %>%
#   summarise(
#     across(all_of(stats_columns), 
#            list(
#              median = ~median(., na.rm = TRUE),
#              iqr = ~IQR(., na.rm = TRUE)
#            ),
#            .names = "{.col}_{.fn}"
#     ),
#     .groups = "drop"
#   )  %>%
#   pivot_longer(
#     cols = -c(年级, 性别),
#     names_to = "维度",
#     values_to = "值"
#   ) %>%
#   separate(col = 维度, into = c("维度", "统计量"), sep = "_") %>%
#   pivot_wider(names_from = 统计量, values_from = 值)

# # 生成可视化图表
# tryCatch({
#   # 按维度生成分面图
#   p <- ggplot(viz_data, aes(x = factor(年级), y = median, fill = 性别)) +
#     geom_col(position = position_dodge()) +
#     geom_errorbar(aes(ymin = median - iqr/2, ymax = median + iqr/2),
#                   width = 0.2, position = position_dodge(0.9)) +
#     facet_wrap(~维度, scales = "free_y", ncol = 3) +
#     labs(title = "营养素养各维度得分分布",
#          x = "年级", y = "中位数 ± IQR") +
#     theme_bw(base_size = 12) +
#     theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
#   # 保存图表
#   ggsave("outputs/group_stats_faceted.png", p, 
#          width = 12, height = 8, dpi = 300)
  
#   # 单独保存每个维度的图表
#   for (dim in unique(viz_data$维度)) {
#     p_dim <- ggplot(viz_data %>% filter(维度 == dim), 
#                    aes(x = factor(年级), y = median, fill = 性别)) +
#       geom_col(position = position_dodge()) +
#       geom_errorbar(aes(ymin = median - iqr/2, ymax = median + iqr/2),
#                     width = 0.2, position = position_dodge(0.9)) +
#       labs(title = paste(dim, "得分分布"),
#            x = "年级", y = "中位数 ± IQR") +
#       theme_bw()
    
#     ggsave(paste0("outputs/", dim, "_distribution.png"), p_dim,
#            width = 8, height = 6, dpi = 300)
#   }
  
#   cat("可视化图表已保存至 outputs/\n")
# }, error = function(e) {
#   cat("可视化过程中出现错误:", e$message, "\n")
# })

# # 2. 影响因素分析（因变量为非正态分布）
# # 2.1 单因素分析，分别就年级、性别和主要照料者类别三个因素对各维度进行单因素分析
# cat("\n正在进行单因素分析...\n")
# library(MASS)

# # 定义分析维度
# analysis_columns <- cols_to_convert

# # 单因素分析函数
# single_factor_analysis <- function(data, variables, factors) {
#   results <- data.frame()
  
#   for (var in variables) {
#     for (factor in factors) {
#       # 检查因子有效性
#       if (n_distinct(data[[factor]]) < 2) next
      
#       # 选择检验方法
#       if (n_distinct(data[[factor]]) > 2) {
#         test <- kruskal.test(data[[var]] ~ data[[factor]])
#         method <- "Kruskal-Wallis"
#       } else {
#         test <- wilcox.test(data[[var]] ~ data[[factor]]) 
#         method <- "Wilcoxon"
#       }
      
#       # 保存结果
#       results <- rbind(results, data.frame(
#         变量 = var,
#         因素 = factor,
#         检验方法 = method,
#         统计量 = round(test$statistic, 2),
#         p值 = round(test$p.value, 4)
#       ))
#     }
#   }
#   return(results)
# }

# # 执行单因素分析
# factors_to_analyze <- c("年级", "性别", "A1") # A1为主要照料者
# univariate_results <- single_factor_analysis(all_data, analysis_columns, factors_to_analyze)

# # 保存结果
# write.csv(univariate_results, "outputs/univariate_analysis.csv", row.names = FALSE)
# cat("单因素分析结果已保存至 outputs/univariate_analysis.csv\n")

# # 2.2 多因素分析，就年级、性别和主要照料者类别三个因素对各维度的影响进行多因素分析
# cat("\n正在进行多因素分析...\n")

# # 将年级转换为有序因子
# all_data$年级_ordinal <- factor(all_data$年级, ordered = TRUE, levels = c(3, 4, 5))

# # 构建比例优势模型
# multivariate_analysis <- function(data, variables, predictors) {
#   results <- data.frame()
  
#   for (var in variables) {
#     formula <- as.formula(paste(var, "~", paste(predictors, collapse = "+")))
    
#     # 拟合有序回归模型
#     model <- polr(formula, data = data, Hess = TRUE)
#     sum_model <- summary(model)
    
#     # 提取结果
#     coef_table <- data.frame(
#       变量 = var,
#       因素 = rownames(sum_model$coefficients),
#       估计值 = round(sum_model$coefficients[, "Value"], 3),
#       p值 = round(2 * pt(abs(sum_model$coefficients[, "t value"]), 
#                        df = model$edf, lower.tail = FALSE), 4)
#     )
    
#     results <- rbind(results, coef_table)
#   }
#   return(results)
# }

# # 执行多因素分析
# predictors <- c("年级_ordinal", "性别", "A1")
# multivariate_results <- multivariate_analysis(all_data, analysis_columns, predictors)

# # 保存结果
# write.csv(multivariate_results, "outputs/multivariate_analysis.csv", row.names = FALSE)
# cat("多因素分析结果已保存至 outputs/multivariate_analysis.csv\n")
