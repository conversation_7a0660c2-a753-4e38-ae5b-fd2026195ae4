# Part one: data combine ----------------------------------------------------------------------

# 加载必要的库
library(readxl)
library(tidyverse)
library(gtsummary)

# 1. 合并学生营养数据、家长营养数据及学生BMI数据

kid_data <- read.csv("outputs/processed_data.csv")
bmi_data <- read.csv("outputs/processed_data_bmi.csv")
parent_data <- read.csv("outputs/processed_data_parents.csv")

# 合并学生营养数据与BMI数据
data <- kid_data %>%
     left_join(bmi_data %>% select(学校名称, 年级, 班级, 学生姓名, 年龄),
        by = c("学校名称", "年级", "班级", "学生姓名"),
        suffix = c("", "_bmi")
    )  %>%
    # 合并家长营养数据
    left_join(
        parent_data,
        by = c(
            "学校名称" = "school",
            "年级" = "Kidgrade",
            "学生姓名" = "Kidname"
        ),
        suffix = c("", "_parent")
    ) %>%
    # 移除可能重复的列
    select(-ends_with("_bmi"), -ends_with(".y"))
cat("数据合并完成！\n")



# 2. 计算BMI并判断健康状况、超重肥胖及行为
cat("正在计算BMI并判断健康状况及超重肥胖...\n")

# 读取BMI判断标准
bmi_standard <- readxl::read_excel("BMI判断标准.xls")

# 处理身高体重单位并计算BMI
data <- data %>%
    mutate(
        # 转换身高单位（假设身高>2的单位是厘米，否则是米）
        身高 = ifelse(身高 > 2, 身高 / 100, 身高),
        # 转换体重单位（假设体重>=200的单位是斤，否则是公斤）
        体重 = ifelse(体重 >= 200, 体重 / 2, 体重),
        BMI = round(体重 / (身高^2), 1)
    ) %>%
    left_join(bmi_standard, by = c("性别", "年龄")) %>%
    mutate(
        healthy = if_else(BMI > 消瘦标准 & BMI< 超重标准, "正常", 
                          "异常", missing = NA_character_),
        overweight = case_when(
          BMI >= 超重标准 ~ "是",
          NA  ~ NA,
          TRUE ~ "否"
        )
    ) 

cat("正在计算行为...\n")    
data <- data %>%
    mutate(    

cat("基本情况变量转换为等级变量...\n")     
data <- data %>%
    mutate(
      年级 = factor(case_when(
        年级 == 3 ~ "3年级",
        年级 == 4 ~ "4年级",
        年级 == 5 ~ "5年级",
        TRUE ~ NA_character_
      ), levels = c("3年级", "4年级", "5年级")),
      性别 = factor(case_when(
        性别==1 ~ "男",
        性别==2 ~ "女",
        TRUE ~ NA_character_
      ),levels = c("男", "女")),
      主要照料者 = factor(case_when(
        A1 == 1 ~ "父母",
        A1 == 2 ~ "祖辈",
        A1 == 3 ~ "其他",
        TRUE ~ NA_character_
      ), levels = c("父母", "祖辈", "其他")),
      主要照料者学历 = factor(case_when(
        A2 == 1 ~ "小学及以下",
        A2 == 2 ~ "初中",
        A2 == 3 ~ "高中",
        A2 == 4 ~ "大专",
        A2 == 5 ~ "本科及以上",
        TRUE ~ NA_character_
      ), levels = c("小学及以下", "初中", "高中", "大专", "本科及以上")),
      家庭经济条件 = factor(case_when(
        A3 == 2 ~ "低",
        A3 == 1 & A4 <= 2 ~ "中",
        A3 == 1 & A4 > 2 ~ "高",
        TRUE ~ NA_character_
      ), levels = c("低", "中", "高"))
    )

# 保存处理后的数据
cat("保存处理后的数据...\n")
write.csv(all_data, "outputs/cleaned_data_NL.csv", row.names = FALSE)

cat("数据清洗完成！\n")


# Part two: data analysis ----------------------------------------------------------------------


#  1.描述性统计分析 ----------------------------------------------------------------------------------


# 1.1 首先判断cols_to_convert中的包含的列数据分布的情况，如果为正态，则采用平均数报告，否则用中位数报告
cols_to_convert <- c("Knowledge_Concepts", "Food_Selection", "Food_Preparation",
                    "Food_Consumption", "Domain_Cognition", "Domain_Skills",
                    "Total_Core_Information")

stats_results <- data.frame(
  Variable = character(),
  Distribution = character(),
  Mean = numeric(),
  Median = numeric(),
  SD = numeric(),
  p25 = numeric(),
  p75 = numeric(),
  IQR = numeric(),
  Recommended_Statistic = character(),
  stringsAsFactors = FALSE
)


for (col_name in cols_to_convert) {

  # 执行Shapiro-Wilk正态性检验
  shapiro_test <- tryCatch({
    shapiro.test(all_data[[col_name]])
  }, error = function(e) {
    cat(sprintf("警告: %s 无法进行Shapiro-Wilk检验: %s\n", col_name, e$message))
    return(list(p.value = 0)) # 默认不是正态分布
  })
  
  # 判断是否为正态分布 (p > 0.05表示可能服从正态分布)
  is_normal <- shapiro_test$p.value > 0.05
  
  # 计算基本统计量
  mean_val <- mean(all_data[[col_name]], na.rm = TRUE)
  median_val <- median(all_data[[col_name]], na.rm = TRUE)
  sd_val <- sd(all_data[[col_name]], na.rm = TRUE)
  p25_val <- quantile(all_data[[col_name]], 0.25, na.rm = TRUE)
  p75_val <- quantile(all_data[[col_name]], 0.75, na.rm = TRUE)
  iqr_val <- IQR(all_data[[col_name]], na.rm = TRUE)
  
  # 确定推荐使用的统计量
  recommended <- ifelse(is_normal, "均值", "中位数")
  
  # 添加到结果数据框
  stats_results <- rbind(stats_results, data.frame(
    Variable = col_name,
    Distribution = ifelse(is_normal, "正态分布", "非正态分布"),
    Mean = mean_val,
    Median = median_val,
    SD = sd_val,
    p25 = p25_val,
    p75 = p75_val,
    IQR = iqr_val,
    Recommended_Statistic = recommended,
    stringsAsFactors = FALSE
  ))
  
  cat(sprintf("%s: %s (p=%f), 推荐使用%s\n", 
              col_name, 
              ifelse(is_normal, "服从正态分布", "不服从正态分布"),
              shapiro_test$p.value,
              recommended))
}


# 创建整体得分分布图
stats_plot <- stats_results %>%
  select(Variable, Median, p25, p75) %>%
  filter(Variable %in% c("Food_Selection", 
                        "Food_Preparation", 
                        "Food_Consumption",
                        "Domain_Cognition", 
                        "Total_Core_Information")) %>%
  mutate(
    Variable = factor(Variable, 
                     levels = c("Food_Selection", 
                              "Food_Preparation", 
                              "Food_Consumption",
                              "Domain_Cognition", 
                              "Total_Core_Information"),
                     labels = c("营养相关知识理念", 
                                "选择食物",
                                "制作食物", 
                                "摄入食物",
                                "营养素养总分"))) %>%
  ggplot(aes(x = Variable, y = Median)) +
  geom_col(fill = "#3498db", alpha = 0.7, width = 0.6) +
  geom_errorbar(aes(ymin = p25, ymax = p75),
                width = 0.2, color = "#2c3e50") +
  labs(title = "营养素养各维度得分",
       x = "维度",
       y = "得分") +
  theme_classic() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    plot.title = element_text(hjust = 0.5, face = "bold")
  )

# 保存图表
ggsave("outputs/dimension_scores_distribution.png", 
       stats_plot, 
       width = 10, 
       height = 6, 
       dpi = 300)

cat("维度得分分布图已保存至 outputs/dimension_scores_distribution.png\n")


# cat("\n正在计算分组统计量...\n")

#  1.2 按照年级、性别、主要照料者类别进行分组，计算营养素养各维度及总分数的中位数和四分位间距（IQR）  --------------------------------------


# # 定义需要统计的列（保持与cols_to_convert一致）
stats_columns <- cols_to_convert

#  使用gtsummary进行分组统计分析
# 创建分组统计分析函数
create_group_analysis <- function(data, group_var, stats_cols, 
                                title_prefix = "分组") {
  # 创建分组统计表
  stats_tbl <- data %>%
    select(all_of(c(group_var, stats_cols))) %>%
    tbl_summary(
      by = group_var,
      statistic = list(all_continuous() ~ "{median} ({p25}, {p75})"),
      digits = everything() ~ 1,
      missing = "no"
    ) %>%
    add_stat_label() %>%
    add_p(
      test = list(all_continuous() ~ 
                   ifelse(length(unique(data[[group_var]])) > 2, 
                          "kruskal.test", "wilcox.test")),
      pvalue_fun = ~ style_pvalue(.x, digits = 3)
    ) %>%
    modify_header(label = "**维度**") %>%
    modify_spanning_header(all_stat_cols() ~ sprintf("**%s**", title_prefix)) %>%
    modify_header(
      update = list(
        all_stat_cols() ~ "{level}"
      )
    )

  # 执行非参数检验
  test_results <- map_dfr(stats_cols, function(col) {
    test_result <- if(length(unique(data[[group_var]])) > 2) {
      kruskal.test(data[[col]] ~ data[[group_var]])
    } else {
      wilcox.test(data[[col]] ~ data[[group_var]])
    }
    
    data.frame(
      维度 = col,
      分组变量 = group_var,
      统计量 = round(test_result$statistic, 2),
      自由度 = if("parameter" %in% names(test_result)) test_result$parameter else NA,
      p值 = round(test_result$p.value, 3)
    )
  })
  
  return(list(table = stats_tbl, tests = test_results))
}

# 使用函数进行分组分析
cat("\n正在使用gtsummary进行分组统计分析...\n")

# 执行各分组的分析
年级_results <- create_group_analysis(all_data, "年级", stats_columns, "年级")
性别_results <- create_group_analysis(all_data, "性别", stats_columns,  "性别")
照料者_results <- create_group_analysis(all_data, "主要照料者", stats_columns,  "主要照料者")
照料者学历_results <- create_group_analysis(all_data, "主要照料者学历", stats_columns,  "主要照料者学历")
家庭经济条件_results <- create_group_analysis(all_data, "家庭经济条件", stats_columns,  "家庭经济条件")

# 合并所有统计表
merged_stats <- tbl_merge(
  tbls = list(年级_results$table, 性别_results$table, 照料者_results$table, 照料者学历_results$table, 家庭经济条件_results$table),
  tab_spanner = c("**年级**", "**性别**", "**主要照料者**", "**主要照料者学历**", "**家庭经济条件**")
)


# 保存合并后的结果
merged_stats %>%
  as_gt() %>%
  gt::gtsave(filename = "outputs/merged_statistics.html")

merged_stats %>%
  as_flex_table() %>%
  flextable::save_as_docx(path = "outputs/merged_statistics.docx")

# 合并所有检验结果并保存
all_tests <- bind_rows(
  年级_results$tests,
  性别_results$tests,
  照料者_results$tests,
  照料者学历_results$tests,
  家庭经济条件_results$tests
)

write.csv(all_tests, "outputs/group_tests_statistics.csv", row.names = FALSE)





# # 2. 多因素分析 ----------------------------------------------------------------------------------


cat("\n正在进行多因素分析...\n")

library(lme4)  # 用于广义线性混合模型
library(broom) # 用于整理回归结果
library(gt)    # 用于创建表格

# 创建多因素分析函数

multifactor_analysis <- function(data, dependent_var) {
  # 构建公式
  formula <- as.formula(paste(dependent_var, 
                            "~ 年级 + 性别 + 主要照料者 + 主要照料者学历 + 家庭经济条件"))
  
  # 拟合广义线性模型（使用高斯分布）
  model <- glm(formula, 
               data = data, 
               family = gaussian(link = "identity"))  # 改用高斯分布
  
  # 提取系数和置信区间
  coef_ci <- confint(model)
  coefs <- coef(model)
  
  # 计算比值比和置信区间
  results <- data.frame(
    维度 = dependent_var,
    变量 = names(coefs)[-1],
    回归系数 = round(coefs[-1], 3),
    CI_lower = round(coef_ci[-1,1], 3),
    CI_upper = round(coef_ci[-1,2], 3),
    p值 = round(summary(model)$coefficients[-1,"Pr(>|t|)"], 3)
  )
  
  return(results)
}

# 对每个维度进行多因素分析
all_multifactor_results <- map_dfr(stats_columns, ~multifactor_analysis(all_data, .))

# 保存结果
write.csv(all_multifactor_results, "outputs/multifactor_analysis_results.csv", row.names = FALSE)



#  3. 儿童营养素养与儿童overweight关系
cat("\n正在进行儿童营养素养与儿童overweight的关系分析...\n")

multifactor_analysis_overweight <- function(data, dependent_var) {
  # 过滤掉overweight为NA的数据
  data_clean <- data %>%
    filter(!is.na(overweight))

  # 确保overweight为因子型，以"否"为参考水平
  data_clean$overweight <- factor(data_clean$overweight, levels = c("否", "是"))

  # 构建公式
  formula <- as.formula(paste("overweight",
                            "~", dependent_var, "+ 年级 + 性别 + 家庭经济条件"))

  # 使用二元logistic回归
  model <- glm(formula,
               data = data_clean,
               family = binomial(link = "logit"))

  # 获取模型摘要
  model_summary <- summary(model)

  # 提取系数和置信区间
  coef_ci <- confint(model)
  coefs <- coef(model)

  # 找到dependent_var对应的系数
  coef_names <- names(coefs)
  dependent_coef_idx <- which(coef_names == dependent_var)

  if(length(dependent_coef_idx) == 0) {
    # 如果没有找到exact match，可能是因为变量名有变化
    warning(paste("未找到变量", dependent_var, "的系数"))
    return(data.frame())
  }

  # 提取dependent_var的系数信息
  estimate <- coefs[dependent_coef_idx]
  std.error <- model_summary$coefficients[dependent_coef_idx, "Std. Error"]
  p_value <- model_summary$coefficients[dependent_coef_idx, "Pr(>|z|)"]

  # 计算置信区间
  ci_lower <- coef_ci[dependent_coef_idx, 1]
  ci_upper <- coef_ci[dependent_coef_idx, 2]

  # 计算OR和置信区间
  OR <- exp(estimate)
  OR_ci_lower <- exp(ci_lower)
  OR_ci_upper <- exp(ci_upper)

  # 创建结果数据框
  results <- data.frame(
    维度 = dependent_var,
    回归系数 = round(estimate, 3),
    标准误 = round(std.error, 3),
    OR = round(OR, 3),
    OR_CI_lower = round(OR_ci_lower, 3),
    OR_CI_upper = round(OR_ci_upper, 3),
    p值 = round(p_value, 3),
    显著性 = ifelse(p_value < 0.001, "***",
                   ifelse(p_value < 0.01, "**",
                          ifelse(p_value < 0.05, "*", ""))),
    stringsAsFactors = FALSE
  )

  return(results)
}

# 对每个维度进行logistic回归分析
all_overweight_results <- map_dfr(stats_columns, ~multifactor_analysis_overweight(all_data, .))

# 保存结果
write.csv(all_overweight_results, "outputs/multifactor_analysis_results_overweight.csv", row.names = FALSE)

cat("儿童营养素养与overweight关系分析完成！结果已保存至 outputs/multifactor_analysis_results_overweight.csv\n")
