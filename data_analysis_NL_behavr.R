# Part one: data combine ----------------------------------------------------------------------

# 加载必要的库
library(readxl)
library(tidyverse)
library(gtsummary)

# 1. 合并学生营养数据、家长营养数据及学生BMI数据

kid_data <- read.csv("outputs/processed_data.csv")
bmi_data <- read.csv("outputs/processed_data_bmi.csv")
parent_data <- read.csv("outputs/processed_data_parents.csv")

# 合并学生营养数据与BMI数据
data <- kid_data %>%
     left_join(bmi_data %>% select(学校名称, 年级, 班级, 学生姓名, 年龄),
        by = c("学校名称", "年级", "班级", "学生姓名"),
        suffix = c("", "_bmi")
    )  %>%
    # 合并家长营养数据
    left_join(
        parent_data,
        by = c(
            "学校名称" = "school",
            "年级" = "Kidgrade",
            "学生姓名" = "Kidname"
        ),
        suffix = c("", "_parent")
    ) %>%
    # 移除可能重复的列
    select(-ends_with("_bmi"), -ends_with(".y"))
cat("数据合并完成！\n")



# 2. 计算BMI并判断健康状况、超重肥胖及行为
cat("正在计算BMI并判断健康状况及超重肥胖...\n")

# 读取BMI判断标准
bmi_standard <- readxl::read_excel("BMI判断标准.xls")

# 处理身高体重单位并计算BMI
data <- data %>%
    mutate(
        # 转换身高单位（假设身高>2的单位是厘米，否则是米）
        身高 = ifelse(身高 > 2, 身高 / 100, 身高),
        # 转换体重单位（假设体重>=200的单位是斤，否则是公斤）
        体重 = ifelse(体重 >= 200, 体重 / 2, 体重),
        BMI = round(体重 / (身高^2), 1)
    ) %>%
    left_join(bmi_standard, by = c("性别", "年龄")) %>%
    mutate(
        healthy = if_else(BMI > 消瘦标准 & BMI< 超重标准, "正常", 
                          "异常", missing = NA_character_),
        overweight = case_when(
          BMI >= 超重标准 ~ "是",
          NA  ~ NA,
          TRUE ~ "否"
        )
    ) 

cat("正在计算行为...\n")
data <- data %>%
    mutate(
    # E1-E4题转换为食物摄入种类1-4（1为1，2为0，NA为NA）
    food_intake_type1 = case_when(
      E1 == 1 ~ 1,
      E1 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type2 = case_when(
      E2 == 1 ~ 1,
      E2 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type3 = case_when(
      E3 == 1 ~ 1,
      E3 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type4 = case_when(
      E4 == 1 ~ 1,
      E4 == 2 ~ 0,
      TRUE ~ NA
    ),
    # F1-F14重命名为食物摄入频次1-14，并根据行为的健康程度赋分（F8、F12-F14反向计分）
    food_intake_freq1 = F1,
    food_intake_freq2 = F2,
    food_intake_freq3 = F3,
    food_intake_freq4 = F4,
    food_intake_freq5 = F5,
    food_intake_freq6 = F6,
    food_intake_freq7 = F7,
    food_intake_freq8 = case_when(
      F8 == 1 ~ 5,
      F8 == 2 ~ 4,
      F8 == 3 ~ 3,
      F8 == 4 ~ 2,
      F8 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq9 = F9,
    food_intake_freq10 = F10,
    food_intake_freq11 = F11,
    food_intake_freq12 = case_when(
      F12 == 1 ~ 5,
      F12 == 2 ~ 4,
      F12 == 3 ~ 3,
      F12 == 4 ~ 2,
      F12 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq13 = case_when(
      F13 == 1 ~ 5,
      F13 == 2 ~ 4,
      F13 == 3 ~ 3,
      F13 == 4 ~ 2,
      F13 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq14 = case_when(
      F14 == 1 ~ 5,
      F14 == 2 ~ 4,
      F14 == 3 ~ 3,
      F14 == 4 ~ 2,
      F14 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    # G1转为water_drink（选择2为1，选择1、3、4为0，选择5为NA）
    water_drink = case_when(
      G1 == 2 ~ 1,
      G1 %in% c(1, 3, 4) ~ 0,
      G1 == 5 ~ NA,
      TRUE ~ NA
    ),
    # G2转换为outdoor_excise（1、2为0，3、4、5为1，NA为NA）
    outdoor_excise = case_when(
      G2 %in% c(1, 2) ~ 0,
      G2 %in% c(3, 4, 5) ~ 1,
      TRUE ~ NA
    ),
    # G3转换为营养比赛（1为1，2为0，NA为NA）
    nutrition_competition = case_when(
      G3 == 1 ~ 1,
      G3 == 2 ~ 0,
      TRUE ~ NA
    ),
    # G4转换为体重监测（1为1，2为0，NA为NA）
    weight_monitoring = case_when(
      G4 == 1 ~ 1,
      G4 == 2 ~ 0,
      TRUE ~ NA
    )
)

cat("基本情况变量转换为等级变量...\n")
data <- data %>%
    mutate(
      年级 = factor(case_when(
        年级 == 3 ~ "3年级",
        年级 == 4 ~ "4年级",
        年级 == 5 ~ "5年级",
        TRUE ~ NA_character_
      ), levels = c("3年级", "4年级", "5年级")),
      性别 = factor(case_when(
        性别==1 ~ "男",
        性别==2 ~ "女",
        TRUE ~ NA_character_
      ),levels = c("男", "女")),
      主要照料者 = factor(case_when(
        A1 == 1 ~ "父母",
        A1 == 2 ~ "祖辈",
        A1 == 3 ~ "其他",
        TRUE ~ NA_character_
      ), levels = c("父母", "祖辈", "其他")),
      主要照料者学历 = factor(case_when(
        A2 == 1 ~ "小学及以下",
        A2 == 2 ~ "初中",
        A2 == 3 ~ "高中",
        A2 == 4 ~ "大专",
        A2 == 5 ~ "本科及以上",
        TRUE ~ NA_character_
      ), levels = c("小学及以下", "初中", "高中", "大专", "本科及以上")),
      家庭经济条件 = factor(case_when(
        A3 == 2 ~ "低",
        A3 == 1 & A4 <= 2 ~ "中",
        A3 == 1 & A4 > 2 ~ "高",
        TRUE ~ NA_character_
      ), levels = c("低", "中", "高"))
    )

# 保存处理后的数据
cat("保存处理后的数据...\n")
write.csv(all_data, "outputs/cleaned_data_NL&behavior.csv", row.names = FALSE)

cat("数据清洗完成！\n")


# Part two: data analysis ----------------------------------------------------------------------

# 计算food_intake_freq总分
cat("计算food_intake_freq总分...\n")
data <- data %>%
  mutate(
    food_intake_freq_total = rowSums(
      select(., food_intake_freq1:food_intake_freq14),
      na.rm = TRUE
    ),
    # 将healthy转换为二分类变量（正常=1，异常=0）
    healthy_binary = case_when(
      healthy == "正常" ~ 1,
      healthy == "异常" ~ 0,
      TRUE ~ NA_real_
    )
  )

# 1. 健康行为描述性分析 -----------------------------------------------------------------------

cat("开始健康行为描述性分析...\n")

library(gtsummary)
library(gt)

# 1.1 F1-14及G1-4各题频次分析
cat("分析F1-14及G1-4各题频次...\n")

# 创建F1-14题目的描述性统计
f_questions <- paste0("F", 1:14)
f_behavior_summary <- data %>%
  select(all_of(f_questions), 学校名称, 年级, 性别,
         主要照料者, 主要照料者学历, 家庭经济条件) %>%
  tbl_summary(
    statistic = list(all_continuous() ~ "{mean} ({sd})",
                     all_categorical() ~ "{n} ({p}%)"),
    digits = everything() ~ 2,
    missing = "no"
  ) %>%
  modify_header(label = "**题目**", stat_0 = "**统计量**") %>%
  modify_caption("F1-F14题目描述性统计")

# G1-4题目的描述性统计
g_questions <- paste0("G", 1:4)
g_behavior_summary <- data %>%
  select(all_of(g_questions), 学校名称, 年级, 性别,
         主要照料者, 主要照料者学历, 家庭经济条件) %>%
  tbl_summary(
    statistic = list(all_continuous() ~ "{mean} ({sd})",
                     all_categorical() ~ "{n} ({p}%)"),
    digits = everything() ~ 2,
    missing = "no"
  ) %>%
  modify_header(label = "**题目**", stat_0 = "**统计量**") %>%
  modify_caption("G1-G4题目描述性统计")

# 保存结果
f_behavior_summary %>%
  as_gt() %>%
  gt::gtsave(filename = "outputs/f_behavior_summary.html")

g_behavior_summary %>%
  as_gt() %>%
  gt::gtsave(filename = "outputs/g_behavior_summary.html")

# 1.2 按分组变量分析F1-14及G1-4的分布差异
cat("分析各组间F1-14及G1-4分布差异...\n")

# 定义分组变量
group_vars <- c("学校名称", "年级", "性别", "主要照料者",
                "主要照料者学历", "家庭经济条件")

# 创建分组分析函数
create_behavior_group_analysis <- function(data, behavior_vars, group_var) {
  data %>%
    select(all_of(c(behavior_vars, group_var))) %>%
    tbl_summary(
      by = group_var,
      statistic = list(all_continuous() ~ "{mean} ({sd})"),
      digits = everything() ~ 2,
      missing = "no"
    ) %>%
    add_p(
      test = list(all_continuous() ~ "kruskal.test"),
      pvalue_fun = ~ style_pvalue(.x, digits = 3)
    ) %>%
    modify_header(label = "**题目**") %>%
    modify_spanning_header(all_stat_cols() ~ sprintf("**%s**", group_var))
}

# 对每个分组变量进行分析
behavior_group_results <- list()
for (group_var in group_vars) {
  cat(sprintf("分析%s组间差异...\n", group_var))

  # F题目分析
  f_result <- create_behavior_group_analysis(data, f_questions, group_var)
  behavior_group_results[[paste0("F_", group_var)]] <- f_result

  # G题目分析
  g_result <- create_behavior_group_analysis(data, g_questions, group_var)
  behavior_group_results[[paste0("G_", group_var)]] <- g_result
}

# 保存分组分析结果
for (name in names(behavior_group_results)) {
  behavior_group_results[[name]] %>%
    as_gt() %>%
    gt::gtsave(filename = sprintf("outputs/%s_group_analysis.html", name))
}

# 1.3 food_intake_freq总分的描述性分析
cat("分析food_intake_freq总分情况...\n")

# 总分描述性统计
freq_total_summary <- data %>%
  select(food_intake_freq_total, 学校名称, 年级, 性别,
         主要照料者, 主要照料者学历, 家庭经济条件) %>%
  tbl_summary(
    statistic = list(all_continuous() ~ "{mean} ({sd}) [{median} ({p25}, {p75})]"),
    digits = everything() ~ 2,
    missing = "no"
  ) %>%
  modify_header(label = "**变量**", stat_0 = "**统计量**") %>%
  modify_caption("food_intake_freq总分描述性统计")

# 按分组变量分析总分差异
freq_total_group_results <- list()
for (group_var in group_vars) {
  cat(sprintf("分析food_intake_freq总分在%s组间差异...\n", group_var))

  result <- data %>%
    select(food_intake_freq_total, all_of(group_var)) %>%
    tbl_summary(
      by = group_var,
      statistic = list(all_continuous() ~ "{mean} ({sd})"),
      digits = everything() ~ 2,
      missing = "no"
    ) %>%
    add_p(
      test = list(all_continuous() ~ "kruskal.test"),
      pvalue_fun = ~ style_pvalue(.x, digits = 3)
    ) %>%
    modify_header(label = "**变量**") %>%
    modify_spanning_header(all_stat_cols() ~ sprintf("**%s**", group_var))

  freq_total_group_results[[group_var]] <- result
}

# 保存总分分析结果
freq_total_summary %>%
  as_gt() %>%
  gt::gtsave(filename = "outputs/freq_total_summary.html")

for (name in names(freq_total_group_results)) {
  freq_total_group_results[[name]] %>%
    as_gt() %>%
    gt::gtsave(filename = sprintf("outputs/freq_total_%s_analysis.html", name))
}

# 2. 营养素养与健康行为相关性分析 --------------------------------------------------------

cat("开始营养素养与健康行为相关性分析...\n")

library(corrr)
library(Hmisc)

# 2.1 营养素养与F1-14各行为题目的相关性（连续vs等级）
cat("分析营养素养与F1-14各行为题目的相关性...\n")

# 使用Spearman相关分析（适用于连续变量与等级变量）
f_correlation_results <- data.frame(
  题目 = character(),
  相关系数 = numeric(),
  p值 = numeric(),
  显著性 = character(),
  stringsAsFactors = FALSE
)

for (f_var in f_questions) {
  if (f_var %in% names(data) && "Total_Core_Information" %in% names(data)) {
    # 移除缺失值
    clean_data <- data %>%
      select(all_of(c("Total_Core_Information", f_var))) %>%
      na.omit()

    if (nrow(clean_data) > 0) {
      cor_test <- cor.test(clean_data$Total_Core_Information,
                          clean_data[[f_var]],
                          method = "spearman")

      significance <- case_when(
        cor_test$p.value < 0.001 ~ "***",
        cor_test$p.value < 0.01 ~ "**",
        cor_test$p.value < 0.05 ~ "*",
        TRUE ~ ""
      )

      f_correlation_results <- rbind(f_correlation_results, data.frame(
        题目 = f_var,
        相关系数 = round(cor_test$estimate, 3),
        p值 = round(cor_test$p.value, 3),
        显著性 = significance,
        stringsAsFactors = FALSE
      ))
    }
  }
}

# 保存F题目相关性结果
write.csv(f_correlation_results,
          "outputs/nutrition_literacy_f_correlation.csv",
          row.names = FALSE)

# 2.2 营养素养与food_intake_freq总分的相关性（连续vs连续）
cat("分析营养素养与food_intake_freq总分的相关性...\n")

if ("Total_Core_Information" %in% names(data) &&
    "food_intake_freq_total" %in% names(data)) {

  clean_data <- data %>%
    select(Total_Core_Information, food_intake_freq_total) %>%
    na.omit()

  if (nrow(clean_data) > 0) {
    # Pearson相关分析
    pearson_cor <- cor.test(clean_data$Total_Core_Information,
                           clean_data$food_intake_freq_total,
                           method = "pearson")

    # Spearman相关分析
    spearman_cor <- cor.test(clean_data$Total_Core_Information,
                            clean_data$food_intake_freq_total,
                            method = "spearman")

    total_correlation_results <- data.frame(
      方法 = c("Pearson", "Spearman"),
      相关系数 = c(round(pearson_cor$estimate, 3),
                  round(spearman_cor$estimate, 3)),
      p值 = c(round(pearson_cor$p.value, 3),
             round(spearman_cor$p.value, 3)),
      显著性 = c(
        case_when(
          pearson_cor$p.value < 0.001 ~ "***",
          pearson_cor$p.value < 0.01 ~ "**",
          pearson_cor$p.value < 0.05 ~ "*",
          TRUE ~ ""
        ),
        case_when(
          spearman_cor$p.value < 0.001 ~ "***",
          spearman_cor$p.value < 0.01 ~ "**",
          spearman_cor$p.value < 0.05 ~ "*",
          TRUE ~ ""
        )
      ),
      stringsAsFactors = FALSE
    )

    # 保存总分相关性结果
    write.csv(total_correlation_results,
              "outputs/nutrition_literacy_total_correlation.csv",
              row.names = FALSE)
  }
}

# 2.3 营养素养与二分类行为变量的相关性分析
cat("分析营养素养与二分类行为变量的相关性...\n")

binary_behavior_vars <- c("water_drink", "outdoor_excise",
                         "nutrition_competition", "weight_monitoring")

binary_correlation_results <- data.frame(
  变量 = character(),
  相关系数 = numeric(),
  p值 = numeric(),
  显著性 = character(),
  stringsAsFactors = FALSE
)

for (var in binary_behavior_vars) {
  if (var %in% names(data) && "Total_Core_Information" %in% names(data)) {
    clean_data <- data %>%
      select(all_of(c("Total_Core_Information", var))) %>%
      na.omit()

    if (nrow(clean_data) > 0) {
      # 使用点双列相关或Spearman相关
      cor_test <- cor.test(clean_data$Total_Core_Information,
                          clean_data[[var]],
                          method = "spearman")

      significance <- case_when(
        cor_test$p.value < 0.001 ~ "***",
        cor_test$p.value < 0.01 ~ "**",
        cor_test$p.value < 0.05 ~ "*",
        TRUE ~ ""
      )

      binary_correlation_results <- rbind(binary_correlation_results,
                                         data.frame(
        变量 = var,
        相关系数 = round(cor_test$estimate, 3),
        p值 = round(cor_test$p.value, 3),
        显著性 = significance,
        stringsAsFactors = FALSE
      ))
    }
  }
}

# 保存二分类变量相关性结果
write.csv(binary_correlation_results,
          "outputs/nutrition_literacy_binary_correlation.csv",
          row.names = FALSE)

# 3. 健康行为与healthy的相关性分析 -------------------------------------------------------

cat("开始健康行为与healthy的相关性分析...\n")

# 3.1 F1-14各行为题目与healthy的相关性（等级vs二分类）
cat("分析F1-14各行为题目与healthy的相关性...\n")

f_healthy_correlation_results <- data.frame(
  题目 = character(),
  相关系数 = numeric(),
  p值 = numeric(),
  显著性 = character(),
  stringsAsFactors = FALSE
)

for (f_var in f_questions) {
  if (f_var %in% names(data) && "healthy_binary" %in% names(data)) {
    clean_data <- data %>%
      select(all_of(c("healthy_binary", f_var))) %>%
      na.omit()

    if (nrow(clean_data) > 0) {
      # 使用Spearman相关分析
      cor_test <- cor.test(clean_data$healthy_binary,
                          clean_data[[f_var]],
                          method = "spearman")

      significance <- case_when(
        cor_test$p.value < 0.001 ~ "***",
        cor_test$p.value < 0.01 ~ "**",
        cor_test$p.value < 0.05 ~ "*",
        TRUE ~ ""
      )

      f_healthy_correlation_results <- rbind(f_healthy_correlation_results,
                                            data.frame(
        题目 = f_var,
        相关系数 = round(cor_test$estimate, 3),
        p值 = round(cor_test$p.value, 3),
        显著性 = significance,
        stringsAsFactors = FALSE
      ))
    }
  }
}

# 保存F题目与healthy相关性结果
write.csv(f_healthy_correlation_results,
          "outputs/f_behavior_healthy_correlation.csv",
          row.names = FALSE)

# 3.2 food_intake_freq总分与healthy的相关性（连续vs二分类）
cat("分析food_intake_freq总分与healthy的相关性...\n")

if ("food_intake_freq_total" %in% names(data) &&
    "healthy_binary" %in% names(data)) {

  clean_data <- data %>%
    select(food_intake_freq_total, healthy_binary) %>%
    na.omit()

  if (nrow(clean_data) > 0) {
    # 使用点双列相关分析
    cor_test <- cor.test(clean_data$food_intake_freq_total,
                        clean_data$healthy_binary,
                        method = "spearman")

    significance <- case_when(
      cor_test$p.value < 0.001 ~ "***",
      cor_test$p.value < 0.01 ~ "**",
      cor_test$p.value < 0.05 ~ "*",
      TRUE ~ ""
    )

    total_healthy_correlation <- data.frame(
      变量 = "food_intake_freq_total",
      相关系数 = round(cor_test$estimate, 3),
      p值 = round(cor_test$p.value, 3),
      显著性 = significance,
      stringsAsFactors = FALSE
    )

    # 保存总分与healthy相关性结果
    write.csv(total_healthy_correlation,
              "outputs/freq_total_healthy_correlation.csv",
              row.names = FALSE)

    # 输出结果摘要
    cat(sprintf("food_intake_freq总分与healthy的相关系数: %.3f (p=%.3f) %s\n",
                cor_test$estimate, cor_test$p.value, significance))
  }
}

# 生成分析报告摘要
cat("\n=== 分析完成摘要 ===\n")
cat("1. 健康行为描述性分析已完成\n")
cat("   - F1-14及G1-4各题频次分析\n")
cat("   - 各组间分布差异性分析\n")
cat("   - food_intake_freq总分分析\n")
cat("2. 营养素养与健康行为相关性分析已完成\n")
cat("   - 营养素养与F1-14各题相关性\n")
cat("   - 营养素养与food_intake_freq总分相关性\n")
cat("   - 营养素养与二分类行为变量相关性\n")
cat("3. 健康行为与healthy相关性分析已完成\n")
cat("   - F1-14各题与healthy相关性\n")
cat("   - food_intake_freq总分与healthy相关性\n")
cat("\n所有结果已保存至outputs文件夹\n")

cat("数据分析完成！\n")

