# Part one data clean -------------------------------------------------------------------------


# 加载必要的库
library(readxl)
library(dplyr)
library(tidyr)

# 1. 导入数据
cat("正在导入家长数据...\n")
parent_data <- read_excel("inputs/家长.xlsx")

# 2. 数据清洗和去重
cat("正在清洗数据...\n")
# 移除重复行
parent_data <- distinct(parent_data, school, Kidgrade, Kidname, .keep_all = TRUE)

# 3. 数据治理
cat("正在进行数据治理...\n")

# 负向题目列表
negative_b <- c(paste0("B", 1:18), "B21", "B23", "B24", "B27", "B28", 
                "B33", "B34", "B37", "B40")

# 处理B开头的Likert-5分题
b_cols <- names(parent_data)[grep("^B\\d+$", names(parent_data))]

for (col in b_cols) {
  if (col %in% negative_b) {
    # 负向题目: 1=2, 2=1.5, 3=1, 4=0.5, 5=0
    parent_data[[paste0(col, "_score")]] <- case_when(
      parent_data[[col]] == 1 ~ 2,
      parent_data[[col]] == 2 ~ 1.5,
      parent_data[[col]] == 3 ~ 1,
      parent_data[[col]] == 4 ~ 0.5,
      parent_data[[col]] == 5 ~ 0,
      TRUE ~ NA_real_
    )
  } else {
    # 正向题目: 1=0, 2=0.5, 3=1, 4=1.5, 5=2
    parent_data[[paste0(col, "_score")]] <- case_when(
      parent_data[[col]] == 1 ~ 0,
      parent_data[[col]] == 2 ~ 0.5,
      parent_data[[col]] == 3 ~ 1,
      parent_data[[col]] == 4 ~ 1.5,
      parent_data[[col]] == 5 ~ 2,
      TRUE ~ NA_real_
    )
  }
}

# 处理C开头的题目
# C题目的正确答案
c_answers <- c(1, 1, 1, 1, 2, 4, 4, 2, 4, 4, 4, 3)

# 处理C1-C12题目
for (i in 1:12) {
  col <- paste0("C", i)
  if (col %in% names(parent_data)) {
    parent_data[[paste0(col, "_score")]] <- ifelse(parent_data[[col]] == c_answers[i], 2, 0)
  }
}

# 4. 计算Core_Information得分
cat("正在计算Core_Information得分...\n")

# 定义Core_Information项目及其对应的题目
core_info_mapping <- list(
  "CI_1" = c("B10"),
  "CI_2" = c("B1"),
  "CI_3" = c("B3", "B4"),
  "CI_4" = c("B2"),
  "CI_5" = c("B11", "B12"),
  "CI_6" = c("B38", "B39", "B40"),
  "CI_7" = c("B27"),
  "CI_8" = c("B25", "B26", "C7"),
  "CI_9" = c("B30", "C8", "B31"),
  "CI_10" = c("B32", "B33", "C9"),
  "CI_11" = c("B24", "C5"),
  "CI_12" = c("C6"),
  "CI_13" = c("B28", "B29", "B34", "B35", "C10", "B36", "B37", "C11"),
  "CI_14" = c("B13"),
  "CI_15" = c("C1", "C2", "C3", "C4", "B14", "B16", "B15", "B21"),
  "CI_16" = c("B17", "B18"),
  "CI_17" = c("B19", "B20", "B22"),
  "CI_18" = c("B7", "B8"),
  "CI_19" = c("B9"),
  "CI_20" = c("B5", "B6", "B23", "B41", "C12")
)

# 计算每个Core_Information的得分
for (ci_name in names(core_info_mapping)) {
  items <- core_info_mapping[[ci_name]]
  score_cols <- paste0(items, "_score")
  
  # 检查这些列是否存在于数据中
  valid_cols <- score_cols[score_cols %in% names(parent_data)]
  
  if (length(valid_cols) > 0) {
    parent_data[[ci_name]] <- rowSums(parent_data[valid_cols], na.rm = TRUE)
    cat(sprintf("已计算%s (基于题目: %s)\n", 
                ci_name, paste(items, collapse=", ")))
  } else {
    cat(sprintf("警告: 无法计算%s，未找到相关得分列\n", ci_name))
    parent_data[[ci_name]] <- NA
  }
}

# 定义Dimension及其对应的Core_Information项目
dimension_mapping <- list(
  "Knowledge_Concepts" = c("CI_1", "CI_2", "CI_3", "CI_4"),
  "Food_Selection" = c("CI_5", "CI_6", "CI_7", "CI_8", "CI_9", "CI_10"), 
  "Food_Preparation" = c("CI_11", "CI_12", "CI_13"), 
  "Food_Consumption" = c("CI_14", "CI_15", "CI_16", "CI_17", "CI_18", "CI_19", "CI_20")
)

# 计算Dimension得分
cat("正在计算Dimension得分...\n")
for (dim_name in names(dimension_mapping)) {
  ci_items <- dimension_mapping[[dim_name]]
  
  # 检查这些Core_Information项目是否已计算
  valid_items <- ci_items[ci_items %in% names(parent_data)]
  
  if (length(valid_items) > 0) {
    parent_data[[dim_name]] <- rowSums(parent_data[valid_items], na.rm = TRUE)
    cat(sprintf("已计算%s (基于Core_Information: %s)\n", 
                dim_name, paste(valid_items, collapse=", ")))
  } else {
    cat(sprintf("警告: 无法计算%s，未找到相关Core_Information项目\n", dim_name))
    parent_data[[dim_name]] <- NA
  }
}

# 定义Domain及其对应的Dimension
domain_mapping <- list(
  "Cognition" = c("Knowledge_Concepts"),
  "Skills" = c("Food_Selection", "Food_Preparation", "Food_Consumption")
)

# 计算Domain得分
cat("正在计算Domain得分...\n")
for (domain_name in names(domain_mapping)) {
  dim_items <- domain_mapping[[domain_name]]
  
  # 检查这些Dimension是否已计算
  valid_dims <- dim_items[dim_items %in% names(parent_data)]
  
  if (length(valid_dims) > 0) {
    parent_data[[paste0("Domain_", domain_name)]] <- rowSums(parent_data[valid_dims], na.rm = TRUE)
    cat(sprintf("已计算Domain_%s (基于Dimension: %s)\n", 
                domain_name, paste(valid_dims, collapse=", ")))
  } else {
    cat(sprintf("警告: 无法计算Domain_%s，未找到相关Dimension\n", domain_name))
    parent_data[[paste0("Domain_", domain_name)]] <- NA
  }
}

# 计算总Core_Information得分（所有Core_Information项目的总和）
ci_cols <- names(core_info_mapping)
valid_ci_cols <- ci_cols[ci_cols %in% names(parent_data)]
if (length(valid_ci_cols) > 0) {
  parent_data[["Total_Core_Information"]] <- rowSums(parent_data[valid_ci_cols], na.rm = TRUE)
  cat(sprintf("已计算Total_Core_Information (基于%d个Core_Information项目)\n", length(valid_ci_cols)))
}

# 计算总得分（基于100分制）
# 定义需要转换的列名
cols_to_convert <- c("Knowledge_Concepts", "Food_Selection", "Food_Preparation",
                    "Food_Consumption", "Domain_Cognition", "Domain_Skills",
                    "Total_Core_Information")

# 使用across函数一次性处理多个列
parent_data  <- parent_data %>%
  mutate(across(all_of(cols_to_convert),
                ~round(. / 106 * 100, 1)))


# 标准化学校名称
cat("正在标准化学校名称...\n")
parent_data <- parent_data %>%
  mutate(school = case_match(
    school,
    2 ~ "珠光小学", 
    3 ~ "向南小学",
    4 ~ "前海小学",
    5 ~ "丽湖学校",
    6 ~ "前海港湾小学",
    7 ~ "蛇口学校",
    8 ~ "太子湾小学",
    9 ~ "松坪学校",
    10 ~ "深湾小学",
    11 ~ "南山小学",
    12 ~ "荔湾小学",
    14 ~ "南头城小学",
    15 ~ "园丁学校",
    16 ~ "南头小学",
    17 ~ "文理一小",
    18 ~ "文理二小",
    19 ~ "荔林小学",
    20 ~ "育才三小",
    21 ~ "北师大附小",
    22 ~ "南山二外",
    23 ~ "海岸小学",
    24 ~ "山海学校",
    NA ~ as.character(school)
  ),
  Kidgrade = case_match(
    Kidgrade,
    1 ~ 3,
    2 ~ 4,
    3 ~ 5,
    NA ~ as.numeric(Kidgrade)
  )
)
# 检查是否有未匹配的学校名称
unmatched_schools <- unique(parent_data$school[is.na(parent_data$school)])
if(length(unmatched_schools) > 0) {
  cat("警告: 发现未匹配的学校名称:\n")
  print(unmatched_schools)
}


# 保存处理后的数据
cat("保存处理后的数据...\n")
write.csv(parent_data, "outputs/processed_data_parents.csv", row.names = FALSE)

cat("数据清洗完成！\n")


# Part two data analysis ----------------------------------------------------------------------

# 加载必要的库
library(gtsummary)

# 1.描述性统计分析
# 首先判断cols_to_convert中的包含的列数据分布的情况，如果为正态，则采用平均数报告，否则用中位数报告
stats_results <- data.frame(
  Variable = character(),
  Distribution = character(),
  Mean = numeric(),
  Median = numeric(),
  SD = numeric(),
  IQR = numeric(),
  Recommended_Statistic = character(),
  stringsAsFactors = FALSE
)

for (col_name in cols_to_convert) {

  # 执行Shapiro-Wilk正态性检验
  shapiro_test <- tryCatch({
    shapiro.test(parent_data[[col_name]])
  }, error = function(e) {
    cat(sprintf("警告: %s 无法进行Shapiro-Wilk检验: %s\n", col_name, e$message))
    return(list(p.value = 0)) # 默认不是正态分布
  })
  
  # 判断是否为正态分布 (p > 0.05表示可能服从正态分布)
  is_normal <- shapiro_test$p.value > 0.05
  
  # 计算基本统计量
  mean_val <- mean(parent_data[[col_name]], na.rm = TRUE)
  median_val <- median(parent_data[[col_name]], na.rm = TRUE)
  sd_val <- sd(parent_data[[col_name]], na.rm = TRUE)
  iqr_val <- IQR(parent_data[[col_name]], na.rm = TRUE)
  
  # 确定推荐使用的统计量
  recommended <- ifelse(is_normal, "均值", "中位数")
  
  # 添加到结果数据框
  stats_results <- rbind(stats_results, data.frame(
    Variable = col_name,
    Distribution = ifelse(is_normal, "正态分布", "非正态分布"),
    Mean = mean_val,
    Median = median_val,
    SD = sd_val,
    IQR = iqr_val,
    Recommended_Statistic = recommended,
    stringsAsFactors = FALSE
  ))
  
  cat(sprintf("%s: %s (p=%f), 推荐使用%s\n", 
              col_name, 
              ifelse(is_normal, "服从正态分布", "不服从正态分布"),
              shapiro_test$p.value,
              recommended))
}
