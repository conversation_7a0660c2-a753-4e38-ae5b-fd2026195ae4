
# Part one data clean -------------------------------------------------------------------------


# 加载必要的库
library(readxl)
library(dplyr)
library(tidyr)

# 1. 导入数据
cat("正在导入数据...\n")
# grade_34 <- read_excel("inputs/三四年级.xls")
# grade_34 <- grade_34 %>%
#   mutate(Grade = "3-4年级")
# 
# grade_5 <- read_excel("inputs/五年级.xls")
# grade_5 <- grade_5 %>%
#   mutate(Grade = "5年级")
# 
# # 合并数据
# all_data <- bind_rows(grade_34, grade_5)

# ... existing code ...

# 1. 导入数据
cat("正在导入数据...\n")
# all_data <- read.csv("inputs/三四五年级.csv", 
#                      header = TRUE,
#                      fileEncoding = "GB18030",  # 使用中文编码
#                      check.names = FALSE)  # 防止列名自动转换
# 20250804数据更新
all_data <- read.csv("inputs/data_250804.csv", 
                     header = TRUE,
                     fileEncoding = "UTF-8",  # 使用中文编码
                     check.names = FALSE)  # 防止列名自动转换

# 2. 数据清洗和去重
cat("正在清洗数据...\n")
# 移除重复行
all_data <- distinct(all_data, 学校名称, 年级, 班级, 学生姓名, .keep_all = TRUE)

# 处理学校名称、年级和班级字段
cat("正在标准化学校名称...\n")
all_data$学校名称 <- case_when(
  grepl("师", all_data$学校名称) ~ "北师大附小",
  grepl("海岸", all_data$学校名称) ~ "海岸小学", 
  grepl("丽湖", all_data$学校名称) ~ "丽湖学校",
  grepl("荔林", all_data$学校名称) ~ "荔林小学",
  grepl("荔湾", all_data$学校名称) ~ "荔湾小学",
  grepl("二外|海德", all_data$学校名称) ~ "南山二外",
  grepl("南山小", all_data$学校名称) ~ "南山小学",
  grepl("南头小", all_data$学校名称) ~ "南头小学",
  grepl("港湾", all_data$学校名称) ~ "前海港湾小学",
  grepl("前海", all_data$学校名称) ~ "前海小学",
  grepl("山海", all_data$学校名称) ~ "山海学校",
  grepl("蛇口", all_data$学校名称) ~ "蛇口学校",
  grepl("深湾", all_data$学校名称) ~ "深湾小学",
  grepl("松坪", all_data$学校名称) ~ "松坪学校",
  grepl("太子湾", all_data$学校名称) ~ "太子湾小学",
  grepl("文理二", all_data$学校名称) ~ "文理二小",
  grepl("文理一", all_data$学校名称) ~ "文理一小",
  grepl("向南", all_data$学校名称) ~ "向南小学",
  grepl("育才三", all_data$学校名称) ~ "育才三小",
  grepl("园丁", all_data$学校名称) ~ "园丁学校",
  grepl("珠光", all_data$学校名称) ~ "珠光小学",
  TRUE ~ all_data$学校名称
)

cat("学校名称标准化完成！\n")

# 标准化年级字段
cat("正在标准化年级字段...\n")
all_data$年级 <- case_when(
  grepl("3", all_data$年级) ~ 3,
  grepl("4", all_data$年级) ~ 4,
  grepl("5", all_data$年级) ~ 5,
  TRUE ~ as.numeric(all_data$年级)
)

# 标准化班级字段
cat("正在标准化班级字段...\n")
all_data$班级 <- gsub("班", "", all_data$班级)

all_data <- all_data %>%
  mutate(Grade = case_when(
    年级 == 3 ~ "3_4年级",
    年级 == 4 ~ "4_4年级",
    年级 == 5 ~ "5年级",
    TRUE ~ NA_character_
  ),
  性别 = case_when(
    性别 == "男" ~ 1,
    性别 ==  "女" ~ 2,
    TRUE ~ NA
  ))

# 3. 数据治理
cat("正在进行数据治理...\n")

# 创建一个函数用于B题目的计分
score_B <- function(data) {
  # 获取所有B开头的列名
  b_cols <- names(data)[grep("^B\\d+$", names(data))]
  
  # 负向题目列表
  negative_b <- c("B1", "B3", "B4", "B6", "B9", "B10", "B11", "B13", "B14", "B15", 
                  "B16", "B17", "B18", "B19", "B20", "B23")
  
  # 处理所有B题目
  for (col in b_cols) {
    if (col %in% negative_b) {
      # 负向题目: 1=2, 2=1.5, 3=1, 4=0.5, 5=0
      data[[paste0(col, "_score")]] <- case_when(
        data[[col]] == 1 ~ 2,
        data[[col]] == 2 ~ 1.5,
        data[[col]] == 3 ~ 1,
        data[[col]] == 4 ~ 0.5,
        data[[col]] == 5 ~ 0,
        TRUE ~ NA_real_
      )
    } else {
      # 正向题目: 1=0, 2=0.5, 3=1, 4=1.5, 5=2
      data[[paste0(col, "_score")]] <- case_when(
        data[[col]] == 1 ~ 0,
        data[[col]] == 2 ~ 0.5,
        data[[col]] == 3 ~ 1,
        data[[col]] == 4 ~ 1.5,
        data[[col]] == 5 ~ 2,
        TRUE ~ NA_real_
      )
    }
  }
  
  return(data)
}

# 创建一个函数用于C题目的计分
score_C <- function(data) {
  # C题目的正确答案
  c_answers <- c(4, 3, 3, 3, 1, 2, 3, 2, 3, 1)
  
  # 处理C1-C10题目
  for (i in 1:10) {
    col <- paste0("C", i)
    if (col %in% names(data)) {
      data[[paste0(col, "_score")]] <- ifelse(data[[col]] == c_answers[i], 2, 0)
    }
  }
  
  return(data)
}

# 创建一个函数用于D题目的计分
score_D <- function(data) {
  # D题目的正确答案
  d_answers <- c(2, 5, 4, 1, 3)
  
  # 处理D1-D5题目
  for (i in 1:5) {
    col <- paste0("D", i)
    if (col %in% names(data)) {
      data[[paste0(col, "_score")]] <- ifelse(data[[col]] == d_answers[i], 1, 0)
    }
  }
  
  return(data)
}

# 应用评分函数
all_data <- score_B(all_data)
all_data <- score_C(all_data)
all_data <- score_D(all_data)

# 4. 根据items.xlsx计算得分
cat("正在计算各维度得分...\n")

# 定义Domain和Dimension结构
domains <- list(
  "Cognition" = list(
    dimensions = c("Knowledge_Concepts")
  ),
  "Skills" = list(
    dimensions = c("Food_Selection", "Food_Preparation", "Food_Consumption")
  )
)

# 定义Core_Information项目及其对应的题目
core_info_mapping <- list(
  # Knowledge & Concepts维度下的7个Core_Information
  "CI_1" = c("B1"),  # 请填入相应题号
  "CI_2" = c("B2"),
  "CI_3" = c("B21", "B22", "B23"),
  "CI_4" = c("C1", "B24"),
  "CI_5" = c("B3", "B4", "D1", "D2", "D3", "D4", "D5", "C2"),
  "CI_6" = c("B5"),
  "CI_7" = c("B25", "B26"),
  # Food Selection维度下的5个Core_Information
  "CI_8" = c("B27", "B28", "B29", "B30", "C3", "C4"),
  "CI_9" = c("B31"),
  "CI_10" = c("B32"),
  "CI_11" = c("B6"),
  "CI_12" = c("B33", "C5", "C6", "C7"),
  # Food Preparation维度下的2个Core_Information
  "CI_13" = c("B34"),
  "CI_14" = c("B35", "B7", "B8", "B9"),
  # Food Consumption维度下的6个Core_Information
  "CI_15" = c("B10"),
  "CI_16" = c("B11"),
  "CI_17" = c("C8"),
  "CI_18" = c("B12", "B13", "B14", "B15", "B16", "B17", "C9"),
  "CI_19" = c("B36", "B37", "B18", "C10"),
  "CI_20" = c("B28", "B19", "B20")
)

# 定义Dimension及其对应的Core_Information项目
dimension_mapping <- list(
  "Knowledge_Concepts" = c("CI_1", "CI_2", "CI_3", "CI_4", "CI_5", "CI_6", "CI_7"),
  "Food_Selection" = c("CI_8", "CI_9", "CI_10", "CI_11", "CI_12"), 
  "Food_Preparation" = c("CI_13", "CI_14"), 
  "Food_Consumption" = c("CI_15", "CI_16", "CI_17", "CI_18", "CI_19", "CI_20")
)

# 计算Core_Information项目得分
cat("计算Core_Information项目得分...\n")
for (ci_name in names(core_info_mapping)) {
  items <- core_info_mapping[[ci_name]]
  
  if (length(items) == 0) {
    cat(sprintf("警告: %s 没有定义题目\n", ci_name))
    next
  }
  
  score_cols <- paste0(items, "_score")
  
  # 检查这些列是否存在于数据中
  valid_cols <- score_cols[score_cols %in% names(all_data)]
  
  if (length(valid_cols) > 0) {
    all_data[[ci_name]] <- rowSums(all_data[valid_cols], na.rm = TRUE)
    cat(sprintf("已计算%s (基于题目: %s)\n", 
                ci_name, paste(items, collapse=", ")))
  } else {
    cat(sprintf("警告: 无法计算%s，未找到相关得分列\n", ci_name))
  }
}

# 计算Dimension得分
cat("计算Dimension得分...\n")
for (dim_name in names(dimension_mapping)) {
  ci_items <- dimension_mapping[[dim_name]]
  
  if (length(ci_items) == 0) {
    cat(sprintf("警告: %s 没有定义Core_Information项目\n", dim_name))
    next
  }
  
  # 检查这些Core_Information项目是否已计算
  valid_items <- ci_items[ci_items %in% names(all_data)]
  
  if (length(valid_items) > 0) {
    all_data[[dim_name]] <- rowSums(all_data[valid_items], na.rm = TRUE)
    cat(sprintf("已计算%s (基于Core_Information: %s)\n", 
                dim_name, paste(valid_items, collapse=", ")))
  } else {
    cat(sprintf("警告: 无法计算%s，未找到相关Core_Information项目\n", dim_name))
  }
}

# 计算Domain得分
cat("计算Domain得分...\n")
for (domain_name in names(domains)) {
  dim_items <- domains[[domain_name]]$dimensions
  
  if (length(dim_items) == 0) {
    cat(sprintf("警告: %s 没有定义Dimension\n", domain_name))
    next
  }
  
  # 检查这些Dimension是否已计算
  valid_dims <- dim_items[dim_items %in% names(all_data)]
  
  if (length(valid_dims) > 0) {
    all_data[[paste0("Domain_", domain_name)]] <- rowSums(all_data[valid_dims], na.rm = TRUE)
    cat(sprintf("已计算Domain_%s (基于Dimension: %s)\n", 
                domain_name, paste(valid_dims, collapse=", ")))
  } else {
    cat(sprintf("警告: 无法计算Domain_%s，未找到相关Dimension\n", domain_name))
  }
}

# 计算总Core_Information得分（所有Core_Information项目的总和）
ci_cols <- names(core_info_mapping)
valid_ci_cols <- ci_cols[ci_cols %in% names(all_data)]
if (length(valid_ci_cols) > 0) {
  all_data[["Total_Core_Information"]] <- rowSums(all_data[valid_ci_cols], na.rm = TRUE)
  cat(sprintf("已计算Total_Core_Information (基于%d个Core_Information项目)\n", length(valid_ci_cols)))
}

cat("维度得分计算完成!\n")

# 计算总得分（基于100分制）
# 定义需要转换的列名
cols_to_convert <- c("Knowledge_Concepts", "Food_Selection", "Food_Preparation",
                    "Food_Consumption", "Domain_Cognition", "Domain_Skills",
                    "Total_Core_Information")

# 使用across函数一次性处理多个列
all_data <- all_data %>%
  mutate(across(all_of(cols_to_convert),
                ~round(. / ifelse(Grade == "3-4年级", 95, 101) * 100, 1)))  


# 保存处理后的数据
cat("保存处理后的数据...\n")
write.csv(all_data, "outputs/processed_data.csv", row.names = FALSE)

cat("数据清洗完成！\n")