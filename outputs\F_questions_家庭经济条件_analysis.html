<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<style>body{background-color:white;}</style>


</head>
<body>
<div id="ildxbmphhd" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;">
  <style>#ildxbmphhd table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#ildxbmphhd thead, #ildxbmphhd tbody, #ildxbmphhd tfoot, #ildxbmphhd tr, #ildxbmphhd td, #ildxbmphhd th {
  border-style: none;
}

#ildxbmphhd p {
  margin: 0;
  padding: 0;
}

#ildxbmphhd .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#ildxbmphhd .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#ildxbmphhd .gt_title {
  color: #333333;
  font-size: 125%;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#ildxbmphhd .gt_subtitle {
  color: #333333;
  font-size: 85%;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#ildxbmphhd .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#ildxbmphhd .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#ildxbmphhd .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#ildxbmphhd .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#ildxbmphhd .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#ildxbmphhd .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#ildxbmphhd .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#ildxbmphhd .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#ildxbmphhd .gt_spanner_row {
  border-bottom-style: hidden;
}

#ildxbmphhd .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#ildxbmphhd .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#ildxbmphhd .gt_from_md > :first-child {
  margin-top: 0;
}

#ildxbmphhd .gt_from_md > :last-child {
  margin-bottom: 0;
}

#ildxbmphhd .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#ildxbmphhd .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#ildxbmphhd .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#ildxbmphhd .gt_row_group_first td {
  border-top-width: 2px;
}

#ildxbmphhd .gt_row_group_first th {
  border-top-width: 2px;
}

#ildxbmphhd .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#ildxbmphhd .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#ildxbmphhd .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#ildxbmphhd .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#ildxbmphhd .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#ildxbmphhd .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#ildxbmphhd .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#ildxbmphhd .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#ildxbmphhd .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#ildxbmphhd .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#ildxbmphhd .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#ildxbmphhd .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#ildxbmphhd .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#ildxbmphhd .gt_left {
  text-align: left;
}

#ildxbmphhd .gt_center {
  text-align: center;
}

#ildxbmphhd .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#ildxbmphhd .gt_font_normal {
  font-weight: normal;
}

#ildxbmphhd .gt_font_bold {
  font-weight: bold;
}

#ildxbmphhd .gt_font_italic {
  font-style: italic;
}

#ildxbmphhd .gt_super {
  font-size: 65%;
}

#ildxbmphhd .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#ildxbmphhd .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#ildxbmphhd .gt_indent_1 {
  text-indent: 5px;
}

#ildxbmphhd .gt_indent_2 {
  text-indent: 10px;
}

#ildxbmphhd .gt_indent_3 {
  text-indent: 15px;
}

#ildxbmphhd .gt_indent_4 {
  text-indent: 20px;
}

#ildxbmphhd .gt_indent_5 {
  text-indent: 25px;
}
</style>
  <table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false">
  <thead>
    <tr class="gt_col_headings gt_spanner_row">
      <th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;题目&lt;/strong&gt;"><strong>题目</strong></th>
      <th class="gt_center gt_columns_top_border gt_column_spanner_outer" rowspan="1" colspan="3" scope="colgroup" id="&lt;strong&gt;家庭经济条件&lt;/strong&gt;">
        <span class="gt_column_spanner"><strong>家庭经济条件</strong></span>
      </th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="2" colspan="1" scope="col" id="&lt;strong&gt;p-value&lt;/strong&gt;&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;2&lt;/sup&gt;&lt;/span&gt;"><strong>p-value</strong><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span></th>
    </tr>
    <tr class="gt_col_headings">
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;低&lt;/strong&gt;, N = 866&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>低</strong>, N = 866<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;中&lt;/strong&gt;, N = 321&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>中</strong>, N = 321<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;高&lt;/strong&gt;, N = 1,236&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>高</strong>, N = 1,236<span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
    </tr>
  </thead>
  <tbody class="gt_table_body">
    <tr><td headers="label" class="gt_row gt_left">F1</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.946</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">761.0 (87.9%)</td>
<td headers="stat_2" class="gt_row gt_center">280.0 (87.2%)</td>
<td headers="stat_3" class="gt_row gt_center">1,099.0 (89.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">61.0 (7.0%)</td>
<td headers="stat_2" class="gt_row gt_center">24.0 (7.5%)</td>
<td headers="stat_3" class="gt_row gt_center">72.0 (5.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">26.0 (3.0%)</td>
<td headers="stat_2" class="gt_row gt_center">11.0 (3.4%)</td>
<td headers="stat_3" class="gt_row gt_center">38.0 (3.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">12.0 (1.4%)</td>
<td headers="stat_2" class="gt_row gt_center">3.0 (0.9%)</td>
<td headers="stat_3" class="gt_row gt_center">18.0 (1.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">6.0 (0.7%)</td>
<td headers="stat_2" class="gt_row gt_center">3.0 (0.9%)</td>
<td headers="stat_3" class="gt_row gt_center">8.0 (0.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F2</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.004</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">513.0 (59.2%)</td>
<td headers="stat_2" class="gt_row gt_center">190.0 (59.2%)</td>
<td headers="stat_3" class="gt_row gt_center">811.0 (65.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">124.0 (14.3%)</td>
<td headers="stat_2" class="gt_row gt_center">48.0 (15.0%)</td>
<td headers="stat_3" class="gt_row gt_center">183.0 (14.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">112.0 (12.9%)</td>
<td headers="stat_2" class="gt_row gt_center">43.0 (13.4%)</td>
<td headers="stat_3" class="gt_row gt_center">137.0 (11.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">90.0 (10.4%)</td>
<td headers="stat_2" class="gt_row gt_center">32.0 (10.0%)</td>
<td headers="stat_3" class="gt_row gt_center">90.0 (7.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">27.0 (3.1%)</td>
<td headers="stat_2" class="gt_row gt_center">8.0 (2.5%)</td>
<td headers="stat_3" class="gt_row gt_center">14.0 (1.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F3</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">523.0 (60.4%)</td>
<td headers="stat_2" class="gt_row gt_center">185.0 (57.6%)</td>
<td headers="stat_3" class="gt_row gt_center">826.0 (66.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">110.0 (12.7%)</td>
<td headers="stat_2" class="gt_row gt_center">56.0 (17.4%)</td>
<td headers="stat_3" class="gt_row gt_center">171.0 (13.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">116.0 (13.4%)</td>
<td headers="stat_2" class="gt_row gt_center">39.0 (12.1%)</td>
<td headers="stat_3" class="gt_row gt_center">132.0 (10.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">75.0 (8.7%)</td>
<td headers="stat_2" class="gt_row gt_center">25.0 (7.8%)</td>
<td headers="stat_3" class="gt_row gt_center">78.0 (6.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">42.0 (4.8%)</td>
<td headers="stat_2" class="gt_row gt_center">16.0 (5.0%)</td>
<td headers="stat_3" class="gt_row gt_center">28.0 (2.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F4</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">457.0 (52.8%)</td>
<td headers="stat_2" class="gt_row gt_center">163.0 (50.8%)</td>
<td headers="stat_3" class="gt_row gt_center">782.0 (63.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">168.0 (19.4%)</td>
<td headers="stat_2" class="gt_row gt_center">51.0 (15.9%)</td>
<td headers="stat_3" class="gt_row gt_center">182.0 (14.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">132.0 (15.2%)</td>
<td headers="stat_2" class="gt_row gt_center">62.0 (19.3%)</td>
<td headers="stat_3" class="gt_row gt_center">164.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">69.0 (8.0%)</td>
<td headers="stat_2" class="gt_row gt_center">34.0 (10.6%)</td>
<td headers="stat_3" class="gt_row gt_center">89.0 (7.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">40.0 (4.6%)</td>
<td headers="stat_2" class="gt_row gt_center">11.0 (3.4%)</td>
<td headers="stat_3" class="gt_row gt_center">18.0 (1.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F5</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">435.0 (50.2%)</td>
<td headers="stat_2" class="gt_row gt_center">140.0 (43.6%)</td>
<td headers="stat_3" class="gt_row gt_center">667.0 (54.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">127.0 (14.7%)</td>
<td headers="stat_2" class="gt_row gt_center">50.0 (15.6%)</td>
<td headers="stat_3" class="gt_row gt_center">210.0 (17.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">135.0 (15.6%)</td>
<td headers="stat_2" class="gt_row gt_center">57.0 (17.8%)</td>
<td headers="stat_3" class="gt_row gt_center">178.0 (14.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">112.0 (12.9%)</td>
<td headers="stat_2" class="gt_row gt_center">43.0 (13.4%)</td>
<td headers="stat_3" class="gt_row gt_center">135.0 (10.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">57.0 (6.6%)</td>
<td headers="stat_2" class="gt_row gt_center">31.0 (9.7%)</td>
<td headers="stat_3" class="gt_row gt_center">45.0 (3.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F6</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.013</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">143.0 (16.5%)</td>
<td headers="stat_2" class="gt_row gt_center">57.0 (17.8%)</td>
<td headers="stat_3" class="gt_row gt_center">270.0 (21.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">105.0 (12.1%)</td>
<td headers="stat_2" class="gt_row gt_center">56.0 (17.4%)</td>
<td headers="stat_3" class="gt_row gt_center">151.0 (12.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">218.0 (25.2%)</td>
<td headers="stat_2" class="gt_row gt_center">80.0 (24.9%)</td>
<td headers="stat_3" class="gt_row gt_center">299.0 (24.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">238.0 (27.5%)</td>
<td headers="stat_2" class="gt_row gt_center">79.0 (24.6%)</td>
<td headers="stat_3" class="gt_row gt_center">329.0 (26.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">162.0 (18.7%)</td>
<td headers="stat_2" class="gt_row gt_center">49.0 (15.3%)</td>
<td headers="stat_3" class="gt_row gt_center">186.0 (15.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F7</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.002</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">291.0 (33.6%)</td>
<td headers="stat_2" class="gt_row gt_center">114.0 (35.5%)</td>
<td headers="stat_3" class="gt_row gt_center">480.0 (38.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">136.0 (15.7%)</td>
<td headers="stat_2" class="gt_row gt_center">58.0 (18.1%)</td>
<td headers="stat_3" class="gt_row gt_center">201.0 (16.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">163.0 (18.8%)</td>
<td headers="stat_2" class="gt_row gt_center">60.0 (18.7%)</td>
<td headers="stat_3" class="gt_row gt_center">262.0 (21.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">190.0 (21.9%)</td>
<td headers="stat_2" class="gt_row gt_center">51.0 (15.9%)</td>
<td headers="stat_3" class="gt_row gt_center">201.0 (16.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">86.0 (9.9%)</td>
<td headers="stat_2" class="gt_row gt_center">38.0 (11.8%)</td>
<td headers="stat_3" class="gt_row gt_center">91.0 (7.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F8</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.462</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">48.0 (5.5%)</td>
<td headers="stat_2" class="gt_row gt_center">18.0 (5.6%)</td>
<td headers="stat_3" class="gt_row gt_center">58.0 (4.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">64.0 (7.4%)</td>
<td headers="stat_2" class="gt_row gt_center">31.0 (9.7%)</td>
<td headers="stat_3" class="gt_row gt_center">91.0 (7.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">98.0 (11.3%)</td>
<td headers="stat_2" class="gt_row gt_center">43.0 (13.4%)</td>
<td headers="stat_3" class="gt_row gt_center">168.0 (13.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">353.0 (40.8%)</td>
<td headers="stat_2" class="gt_row gt_center">136.0 (42.4%)</td>
<td headers="stat_3" class="gt_row gt_center">502.0 (40.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">303.0 (35.0%)</td>
<td headers="stat_2" class="gt_row gt_center">93.0 (29.0%)</td>
<td headers="stat_3" class="gt_row gt_center">416.0 (33.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F9</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.007</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">177.0 (20.4%)</td>
<td headers="stat_2" class="gt_row gt_center">73.0 (22.7%)</td>
<td headers="stat_3" class="gt_row gt_center">283.0 (22.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">141.0 (16.3%)</td>
<td headers="stat_2" class="gt_row gt_center">51.0 (15.9%)</td>
<td headers="stat_3" class="gt_row gt_center">245.0 (19.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">207.0 (23.9%)</td>
<td headers="stat_2" class="gt_row gt_center">98.0 (30.5%)</td>
<td headers="stat_3" class="gt_row gt_center">310.0 (25.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">224.0 (25.9%)</td>
<td headers="stat_2" class="gt_row gt_center">69.0 (21.5%)</td>
<td headers="stat_3" class="gt_row gt_center">278.0 (22.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">117.0 (13.5%)</td>
<td headers="stat_2" class="gt_row gt_center">30.0 (9.3%)</td>
<td headers="stat_3" class="gt_row gt_center">119.0 (9.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F10</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center"><0.001</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">97.0 (11.2%)</td>
<td headers="stat_2" class="gt_row gt_center">42.0 (13.1%)</td>
<td headers="stat_3" class="gt_row gt_center">192.0 (15.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">106.0 (12.2%)</td>
<td headers="stat_2" class="gt_row gt_center">42.0 (13.1%)</td>
<td headers="stat_3" class="gt_row gt_center">203.0 (16.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">214.0 (24.7%)</td>
<td headers="stat_2" class="gt_row gt_center">85.0 (26.5%)</td>
<td headers="stat_3" class="gt_row gt_center">345.0 (27.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">305.0 (35.2%)</td>
<td headers="stat_2" class="gt_row gt_center">96.0 (29.9%)</td>
<td headers="stat_3" class="gt_row gt_center">331.0 (26.8%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">144.0 (16.6%)</td>
<td headers="stat_2" class="gt_row gt_center">56.0 (17.4%)</td>
<td headers="stat_3" class="gt_row gt_center">164.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F11</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.004</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">96.0 (11.1%)</td>
<td headers="stat_2" class="gt_row gt_center">35.0 (10.9%)</td>
<td headers="stat_3" class="gt_row gt_center">174.0 (14.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">102.0 (11.8%)</td>
<td headers="stat_2" class="gt_row gt_center">36.0 (11.2%)</td>
<td headers="stat_3" class="gt_row gt_center">164.0 (13.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">159.0 (18.4%)</td>
<td headers="stat_2" class="gt_row gt_center">80.0 (24.9%)</td>
<td headers="stat_3" class="gt_row gt_center">265.0 (21.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">273.0 (31.5%)</td>
<td headers="stat_2" class="gt_row gt_center">91.0 (28.3%)</td>
<td headers="stat_3" class="gt_row gt_center">381.0 (30.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">236.0 (27.3%)</td>
<td headers="stat_2" class="gt_row gt_center">79.0 (24.6%)</td>
<td headers="stat_3" class="gt_row gt_center">251.0 (20.3%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F12</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.453</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">38.0 (4.4%)</td>
<td headers="stat_2" class="gt_row gt_center">17.0 (5.3%)</td>
<td headers="stat_3" class="gt_row gt_center">48.0 (3.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">52.0 (6.0%)</td>
<td headers="stat_2" class="gt_row gt_center">18.0 (5.6%)</td>
<td headers="stat_3" class="gt_row gt_center">75.0 (6.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">91.0 (10.5%)</td>
<td headers="stat_2" class="gt_row gt_center">45.0 (14.0%)</td>
<td headers="stat_3" class="gt_row gt_center">147.0 (11.9%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">322.0 (37.2%)</td>
<td headers="stat_2" class="gt_row gt_center">124.0 (38.6%)</td>
<td headers="stat_3" class="gt_row gt_center">496.0 (40.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">363.0 (41.9%)</td>
<td headers="stat_2" class="gt_row gt_center">117.0 (36.4%)</td>
<td headers="stat_3" class="gt_row gt_center">469.0 (38.0%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F13</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.754</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">33.0 (3.8%)</td>
<td headers="stat_2" class="gt_row gt_center">19.0 (5.9%)</td>
<td headers="stat_3" class="gt_row gt_center">57.0 (4.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">62.0 (7.2%)</td>
<td headers="stat_2" class="gt_row gt_center">22.0 (6.9%)</td>
<td headers="stat_3" class="gt_row gt_center">89.0 (7.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">118.0 (13.6%)</td>
<td headers="stat_2" class="gt_row gt_center">46.0 (14.3%)</td>
<td headers="stat_3" class="gt_row gt_center">193.0 (15.6%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">337.0 (38.9%)</td>
<td headers="stat_2" class="gt_row gt_center">127.0 (39.6%)</td>
<td headers="stat_3" class="gt_row gt_center">462.0 (37.4%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">316.0 (36.5%)</td>
<td headers="stat_2" class="gt_row gt_center">107.0 (33.3%)</td>
<td headers="stat_3" class="gt_row gt_center">434.0 (35.1%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">F14</td>
<td headers="stat_1" class="gt_row gt_center"><br /></td>
<td headers="stat_2" class="gt_row gt_center"><br /></td>
<td headers="stat_3" class="gt_row gt_center"><br /></td>
<td headers="p.value" class="gt_row gt_center">0.034</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    1</td>
<td headers="stat_1" class="gt_row gt_center">33.0 (3.8%)</td>
<td headers="stat_2" class="gt_row gt_center">18.0 (5.6%)</td>
<td headers="stat_3" class="gt_row gt_center">68.0 (5.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    2</td>
<td headers="stat_1" class="gt_row gt_center">52.0 (6.0%)</td>
<td headers="stat_2" class="gt_row gt_center">21.0 (6.5%)</td>
<td headers="stat_3" class="gt_row gt_center">107.0 (8.7%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3</td>
<td headers="stat_1" class="gt_row gt_center">129.0 (14.9%)</td>
<td headers="stat_2" class="gt_row gt_center">48.0 (15.0%)</td>
<td headers="stat_3" class="gt_row gt_center">191.0 (15.5%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4</td>
<td headers="stat_1" class="gt_row gt_center">335.0 (38.7%)</td>
<td headers="stat_2" class="gt_row gt_center">137.0 (42.7%)</td>
<td headers="stat_3" class="gt_row gt_center">496.0 (40.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5</td>
<td headers="stat_1" class="gt_row gt_center">317.0 (36.6%)</td>
<td headers="stat_2" class="gt_row gt_center">97.0 (30.2%)</td>
<td headers="stat_3" class="gt_row gt_center">373.0 (30.2%)</td>
<td headers="p.value" class="gt_row gt_center"><br /></td></tr>
  </tbody>
  
  <tfoot class="gt_footnotes">
    <tr>
      <td class="gt_footnote" colspan="5"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span> n (%)</td>
    </tr>
    <tr>
      <td class="gt_footnote" colspan="5"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>2</sup></span> Pearson’s Chi-squared test</td>
    </tr>
  </tfoot>
</table>
</div>
</body>
</html>
