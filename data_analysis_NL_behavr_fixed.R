# Part one: data combine ----------------------------------------------------------------------

# 加载必要的库
library(readxl)
library(tidyverse)
library(gtsummary)

# 安装并加载必要的包
packages_needed <- c("ppcor", "corrr", "Hmisc")
for (pkg in packages_needed) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# 1. 合并学生营养数据、家长营养数据及学生BMI数据

kid_data <- read.csv("processed_data.csv")
bmi_data <- read.csv("processed_data_bmi.csv")
parent_data <- read.csv("processed_data_parents.csv")

# 合并学生营养数据与BMI数据
data <- kid_data %>%
     left_join(bmi_data %>% select(学校名称, 年级, 班级, 学生姓名, 年龄),
        by = c("学校名称", "年级", "班级", "学生姓名"),
        suffix = c("", "_bmi")
    )  %>%
    # 合并家长营养数据
    left_join(
        parent_data,
        by = c(
            "学校名称" = "school",
            "年级" = "Kidgrade",
            "学生姓名" = "Kidname"
        ),
        suffix = c("", "_parent")
    ) %>%
    # 移除可能重复的列
    select(-ends_with("_bmi"), -ends_with(".y"))
cat("数据合并完成！\n")

# 2. 计算BMI并判断健康状况、超重肥胖及行为
cat("正在计算BMI并判断健康状况及超重肥胖...\n")

# 读取BMI判断标准
bmi_standard <- readxl::read_excel("BMI判断标准.xls")

# 处理身高体重单位并计算BMI
data <- data %>%
    mutate(
        # 转换身高单位（假设身高>2的单位是厘米，否则是米）
        身高 = ifelse(身高 > 2, 身高 / 100, 身高),
        # 转换体重单位（假设体重>=200的单位是斤，否则是公斤）
        体重 = ifelse(体重 >= 200, 体重 / 2, 体重),
        BMI = round(体重 / (身高^2), 1)
    ) %>%
    left_join(bmi_standard, by = c("性别", "年龄")) %>%
    mutate(
        healthy = if_else(BMI > 消瘦标准 & BMI< 超重标准, "正常", 
                          "异常", missing = NA_character_),
        overweight = case_when(
          BMI >= 超重标准 ~ "是",
          NA  ~ NA,
          TRUE ~ "否"
        )
    ) 

cat("正在计算行为...\n")    
data <- data %>%
    mutate(
    # E1-E4题转换为食物摄入种类1-4（1为1，2为0，NA为NA）
    food_intake_type1 = case_when(
      E1 == 1 ~ 1,
      E1 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type2 = case_when(
      E2 == 1 ~ 1,
      E2 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type3 = case_when(
      E3 == 1 ~ 1,
      E3 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type4 = case_when(
      E4 == 1 ~ 1,
      E4 == 2 ~ 0,
      TRUE ~ NA
    ),
    # F1-F14重命名为食物摄入频次1-14，并根据行为的健康程度赋分（F8、F12-F14反向计分）
    food_intake_freq1 = F1,
    food_intake_freq2 = F2,
    food_intake_freq3 = F3,
    food_intake_freq4 = F4,
    food_intake_freq5 = F5,
    food_intake_freq6 = F6,
    food_intake_freq7 = F7,
    food_intake_freq8 = case_when(
      F8 == 1 ~ 5,
      F8 == 2 ~ 4,
      F8 == 3 ~ 3,
      F8 == 4 ~ 2,
      F8 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq9 = F9,
    food_intake_freq10 = F10,
    food_intake_freq11 = F11,
    food_intake_freq12 = case_when(
      F12 == 1 ~ 5,
      F12 == 2 ~ 4,
      F12 == 3 ~ 3,
      F12 == 4 ~ 2,
      F12 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq13 = case_when(
      F13 == 1 ~ 5,
      F13 == 2 ~ 4,
      F13 == 3 ~ 3,
      F13 == 4 ~ 2,
      F13 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq14 = case_when(
      F14 == 1 ~ 5,
      F14 == 2 ~ 4,
      F14 == 3 ~ 3,
      F14 == 4 ~ 2,
      F14 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    # G1转为water_drink（选择2为1，选择1、3、4为0，选择5为NA）
    water_drink = case_when(
      G1 == 2 ~ 1,
      G1 %in% c(1, 3, 4) ~ 0,
      G1 == 5 ~ NA,
      TRUE ~ NA
    ),
    # G2转换为outdoor_excise（1、2为0，3、4、5为1，NA为NA）
    outdoor_excise = case_when(
      G2 %in% c(1, 2) ~ 0,
      G2 %in% c(3, 4, 5) ~ 1,
      TRUE ~ NA
    ),
    # G3转换为营养比赛（1为1，2为0，NA为NA）
    nutrition_competition = case_when(
      G3 == 1 ~ 1,
      G3 == 2 ~ 0,
      TRUE ~ NA
    ),
    # G4转换为体重监测（1为1，2为0，NA为NA）
    weight_monitoring = case_when(
      G4 == 1 ~ 1,
      G4 == 2 ~ 0,
      TRUE ~ NA
    )
)

cat("基本情况变量转换为等级变量...\n")     
data <- data %>%
    mutate(
      年级 = factor(case_when(
        年级 == 3 ~ "3年级",
        年级 == 4 ~ "4年级",
        年级 == 5 ~ "5年级",
        TRUE ~ NA_character_
      ), levels = c("3年级", "4年级", "5年级")),
      性别 = factor(case_when(
        性别 == 1 ~ "男",
        性别 == 2 ~ "女",
        TRUE ~ NA_character_
      ), levels = c("男", "女")),
      主要照料者 = factor(case_when(
        A1 == 1 ~ "父母",
        A1 == 2 ~ "祖辈",
        A1 == 3 ~ "其他",
        TRUE ~ NA_character_
      ), levels = c("父母", "祖辈", "其他")),
      主要照料者学历 = factor(case_when(
        A2 == 1 ~ "小学及以下",
        A2 == 2 ~ "初中",
        A2 == 3 ~ "高中",
        A2 == 4 ~ "大专",
        A2 == 5 ~ "本科及以上",
        TRUE ~ NA_character_
      ), levels = c("小学及以下", "初中", "高中", "大专", "本科及以上")),
      家庭经济条件 = factor(case_when(
        A3 == 2 ~ "低",
        A3 == 1 & A4 <= 2 ~ "中",
        A3 == 1 & A4 > 2 ~ "高",
        TRUE ~ NA_character_
      ), levels = c("低", "中", "高"))
    )

# 保存处理后的数据
cat("保存处理后的数据...\n")
write.csv(data, "outputs/cleaned_data_NL&behavior.csv", row.names = FALSE)

cat("数据清洗完成！\n")

# Part two: data analysis ----------------------------------------------------------------------

# 计算food_intake_freq总分
cat("计算food_intake_freq总分...\n")
data <- data %>%
  mutate(
    food_intake_freq_total = rowSums(
      select(., food_intake_freq1:food_intake_freq14), 
      na.rm = TRUE
    ),
    # 将healthy转换为二分类变量（正常=1，异常=0）
    healthy_binary = case_when(
      healthy == "正常" ~ 1,
      healthy == "异常" ~ 0,
      TRUE ~ NA_real_
    )
  )

cat("数据预处理完成，开始分析...\n")

# 1. 健康行为描述性分析 -----------------------------------------------------------------------

cat("1. 开始健康行为描述性分析...\n")

# 1.1 基本描述性统计
f_questions <- paste0("F", 1:14)
g_questions <- paste0("G", 1:4)

# F1-14题目频次分析
cat("分析F1-14各题频次分布...\n")
f_freq_results <- list()
for (f_var in f_questions) {
  if (f_var %in% names(data)) {
    freq_table <- data %>%
      count(.data[[f_var]], name = "计数") %>%
      mutate(
        题目 = f_var,
        选项 = .data[[f_var]],
        占比 = round(计数 / sum(计数) * 100, 2)
      ) %>%
      select(题目, 选项, 计数, 占比)

    f_freq_results[[f_var]] <- freq_table
  }
}
f_all_freq <- do.call(rbind, f_freq_results)
write.csv(f_all_freq, "outputs/f_questions_frequency.csv", row.names = FALSE)

# G1-4题目频次分析
cat("分析G1-4各题频次分布...\n")
g_freq_results <- list()
for (g_var in g_questions) {
  if (g_var %in% names(data)) {
    freq_table <- data %>%
      count(.data[[g_var]], name = "计数") %>%
      mutate(
        题目 = g_var,
        选项 = .data[[g_var]],
        占比 = round(计数 / sum(计数) * 100, 2)
      ) %>%
      select(题目, 选项, 计数, 占比)

    g_freq_results[[g_var]] <- freq_table
  }
}
g_all_freq <- do.call(rbind, g_freq_results)
write.csv(g_all_freq, "outputs/g_questions_frequency.csv", row.names = FALSE)

# food_intake_freq总分分布分析
cat("分析food_intake_freq总分分布...\n")
shapiro_test <- shapiro.test(data$food_intake_freq_total)
is_normal <- shapiro_test$p.value > 0.05

freq_total_stats <- data.frame(
  变量 = "food_intake_freq总分",
  样本量 = sum(!is.na(data$food_intake_freq_total)),
  正态性检验p值 = round(shapiro_test$p.value, 3),
  分布类型 = ifelse(is_normal, "正态分布", "非正态分布"),
  均值 = round(mean(data$food_intake_freq_total, na.rm = TRUE), 2),
  标准差 = round(sd(data$food_intake_freq_total, na.rm = TRUE), 2),
  中位数 = round(median(data$food_intake_freq_total, na.rm = TRUE), 2),
  四分位间距 = round(IQR(data$food_intake_freq_total, na.rm = TRUE), 2),
  推荐统计量 = ifelse(is_normal, "均值±标准差", "中位数(四分位间距)")
)

write.csv(freq_total_stats, "outputs/freq_total_distribution.csv", row.names = FALSE)
cat(sprintf("food_intake_freq总分: %s (p=%.3f)\n",
            ifelse(is_normal, "服从正态分布", "不服从正态分布"),
            shapiro_test$p.value))

# 1.2 简化的组间差异分析（避免Fisher检验错误）
cat("1.2 进行组间差异分析...\n")

group_vars <- c("年级", "性别", "主要照料者", "主要照料者学历", "家庭经济条件")

# 使用简单的描述性统计，避免复杂的统计检验
for (group_var in group_vars) {
  cat(sprintf("分析%s组间差异...\n", group_var))

  # F题目组间差异
  f_group_summary <- data %>%
    select(all_of(c(f_questions, group_var))) %>%
    group_by(.data[[group_var]]) %>%
    summarise(
      across(all_of(f_questions),
             list(mean = ~ mean(.x, na.rm = TRUE),
                  sd = ~ sd(.x, na.rm = TRUE),
                  n = ~ sum(!is.na(.x))),
             .names = "{.col}_{.fn}"),
      .groups = "drop"
    )

  write.csv(f_group_summary,
            sprintf("outputs/F_questions_%s_summary.csv", group_var),
            row.names = FALSE)

  # food_intake_freq总分组间差异
  freq_total_group_summary <- data %>%
    select(food_intake_freq_total, all_of(group_var)) %>%
    group_by(.data[[group_var]]) %>%
    summarise(
      样本量 = sum(!is.na(food_intake_freq_total)),
      均值 = mean(food_intake_freq_total, na.rm = TRUE),
      标准差 = sd(food_intake_freq_total, na.rm = TRUE),
      中位数 = median(food_intake_freq_total, na.rm = TRUE),
      四分位间距 = IQR(food_intake_freq_total, na.rm = TRUE),
      .groups = "drop"
    )

  write.csv(freq_total_group_summary,
            sprintf("outputs/freq_total_%s_summary.csv", group_var),
            row.names = FALSE)
}

# 2. 营养素养与健康行为相关性分析（包含校准因素）----------------------------------------

cat("2. 开始营养素养与健康行为相关性分析...\n")

# 定义校准因素并转换为数值变量
control_vars <- c("年级", "性别", "主要照料者", "主要照料者学历", "家庭经济条件")

data_numeric <- data %>%
  mutate(
    年级_num = as.numeric(年级),
    性别_num = as.numeric(性别),
    主要照料者_num = as.numeric(主要照料者),
    主要照料者学历_num = as.numeric(主要照料者学历),
    家庭经济条件_num = as.numeric(家庭经济条件)
  )

control_vars_num <- c("年级_num", "性别_num", "主要照料者_num",
                     "主要照料者学历_num", "家庭经济条件_num")

# 2.1 营养素养与F1-14各行为题目的偏相关分析
cat("2.1 分析营养素养与F1-14各行为题目的偏相关性...\n")

f_partial_correlation_results <- data.frame(
  题目 = character(),
  简单相关系数 = numeric(),
  简单相关p值 = numeric(),
  偏相关系数 = numeric(),
  偏相关p值 = numeric(),
  简单相关显著性 = character(),
  偏相关显著性 = character(),
  stringsAsFactors = FALSE
)

for (f_var in f_questions) {
  if (f_var %in% names(data_numeric) &&
      "Total_Core_Information" %in% names(data_numeric)) {

    # 准备数据
    analysis_vars <- c("Total_Core_Information", f_var, control_vars_num)
    clean_data <- data_numeric %>%
      select(all_of(analysis_vars)) %>%
      na.omit()

    if (nrow(clean_data) > 10) {
      # 简单相关分析
      simple_cor <- cor.test(clean_data$Total_Core_Information,
                            clean_data[[f_var]],
                            method = "spearman")

      # 偏相关分析
      tryCatch({
        partial_cor <- pcor.test(clean_data$Total_Core_Information,
                                clean_data[[f_var]],
                                clean_data[, control_vars_num],
                                method = "spearman")

        # 显著性标记
        simple_sig <- case_when(
          simple_cor$p.value < 0.001 ~ "***",
          simple_cor$p.value < 0.01 ~ "**",
          simple_cor$p.value < 0.05 ~ "*",
          TRUE ~ ""
        )

        partial_sig <- case_when(
          partial_cor$p.value < 0.001 ~ "***",
          partial_cor$p.value < 0.01 ~ "**",
          partial_cor$p.value < 0.05 ~ "*",
          TRUE ~ ""
        )

        f_partial_correlation_results <- rbind(f_partial_correlation_results,
                                              data.frame(
          题目 = f_var,
          简单相关系数 = round(simple_cor$estimate, 3),
          简单相关p值 = round(simple_cor$p.value, 3),
          偏相关系数 = round(partial_cor$estimate, 3),
          偏相关p值 = round(partial_cor$p.value, 3),
          简单相关显著性 = simple_sig,
          偏相关显著性 = partial_sig,
          stringsAsFactors = FALSE
        ))
      }, error = function(e) {
        cat(sprintf("警告: %s 偏相关分析失败: %s\n", f_var, e$message))
      })
    }
  }
}

write.csv(f_partial_correlation_results,
          "outputs/nutrition_literacy_f_partial_correlation.csv",
          row.names = FALSE)

# 2.2 营养素养与food_intake_freq总分的偏相关分析
cat("2.2 分析营养素养与food_intake_freq总分的偏相关性...\n")

if ("Total_Core_Information" %in% names(data_numeric) &&
    "food_intake_freq_total" %in% names(data_numeric)) {

  analysis_vars <- c("Total_Core_Information", "food_intake_freq_total",
                     control_vars_num)
  clean_data <- data_numeric %>%
    select(all_of(analysis_vars)) %>%
    na.omit()

  if (nrow(clean_data) > 10) {
    # 简单相关分析
    pearson_simple <- cor.test(clean_data$Total_Core_Information,
                              clean_data$food_intake_freq_total,
                              method = "pearson")

    spearman_simple <- cor.test(clean_data$Total_Core_Information,
                               clean_data$food_intake_freq_total,
                               method = "spearman")

    # 偏相关分析
    tryCatch({
      pearson_partial <- pcor.test(clean_data$Total_Core_Information,
                                  clean_data$food_intake_freq_total,
                                  clean_data[, control_vars_num],
                                  method = "pearson")

      spearman_partial <- pcor.test(clean_data$Total_Core_Information,
                                   clean_data$food_intake_freq_total,
                                   clean_data[, control_vars_num],
                                   method = "spearman")

      total_results <- data.frame(
        方法 = c("Pearson简单相关", "Spearman简单相关",
                "Pearson偏相关", "Spearman偏相关"),
        相关系数 = c(round(pearson_simple$estimate, 3),
                    round(spearman_simple$estimate, 3),
                    round(pearson_partial$estimate, 3),
                    round(spearman_partial$estimate, 3)),
        p值 = c(round(pearson_simple$p.value, 3),
               round(spearman_simple$p.value, 3),
               round(pearson_partial$p.value, 3),
               round(spearman_partial$p.value, 3))
      )

      write.csv(total_results,
                "outputs/nutrition_literacy_total_partial_correlation.csv",
                row.names = FALSE)
    }, error = function(e) {
      cat(sprintf("警告: food_intake_freq总分偏相关分析失败: %s\n", e$message))
    })
  }
}

# 2.3 营养素养与二分类行为变量的偏相关分析
cat("2.3 分析营养素养与二分类行为变量的偏相关性...\n")

binary_behavior_vars <- c("water_drink", "outdoor_excise",
                         "nutrition_competition", "weight_monitoring")

binary_results <- data.frame(
  变量 = character(),
  简单相关系数 = numeric(),
  简单相关p值 = numeric(),
  偏相关系数 = numeric(),
  偏相关p值 = numeric(),
  stringsAsFactors = FALSE
)

for (var in binary_behavior_vars) {
  if (var %in% names(data_numeric) &&
      "Total_Core_Information" %in% names(data_numeric)) {

    analysis_vars <- c("Total_Core_Information", var, control_vars_num)
    clean_data <- data_numeric %>%
      select(all_of(analysis_vars)) %>%
      na.omit()

    if (nrow(clean_data) > 10) {
      simple_cor <- cor.test(clean_data$Total_Core_Information,
                            clean_data[[var]],
                            method = "spearman")

      tryCatch({
        partial_cor <- pcor.test(clean_data$Total_Core_Information,
                                clean_data[[var]],
                                clean_data[, control_vars_num],
                                method = "spearman")

        binary_results <- rbind(binary_results, data.frame(
          变量 = var,
          简单相关系数 = round(simple_cor$estimate, 3),
          简单相关p值 = round(simple_cor$p.value, 3),
          偏相关系数 = round(partial_cor$estimate, 3),
          偏相关p值 = round(partial_cor$p.value, 3),
          stringsAsFactors = FALSE
        ))
      }, error = function(e) {
        cat(sprintf("警告: %s 偏相关分析失败: %s\n", var, e$message))
      })
    }
  }
}

write.csv(binary_results,
          "outputs/nutrition_literacy_binary_partial_correlation.csv",
          row.names = FALSE)

# 3. 健康行为与healthy的偏相关分析（校准因素分析）-----------------------------------

cat("3. 开始健康行为与healthy的偏相关分析...\n")

# 3.1 F1-14各行为题目与healthy的偏相关分析
cat("3.1 分析F1-14各行为题目与healthy的偏相关性...\n")

f_healthy_results <- data.frame(
  题目 = character(),
  简单相关系数 = numeric(),
  简单相关p值 = numeric(),
  偏相关系数 = numeric(),
  偏相关p值 = numeric(),
  stringsAsFactors = FALSE
)

for (f_var in f_questions) {
  if (f_var %in% names(data_numeric) && "healthy_binary" %in% names(data_numeric)) {

    analysis_vars <- c("healthy_binary", f_var, control_vars_num)
    clean_data <- data_numeric %>%
      select(all_of(analysis_vars)) %>%
      na.omit()

    if (nrow(clean_data) > 10) {
      simple_cor <- cor.test(clean_data$healthy_binary,
                            clean_data[[f_var]],
                            method = "spearman")

      tryCatch({
        partial_cor <- pcor.test(clean_data$healthy_binary,
                                clean_data[[f_var]],
                                clean_data[, control_vars_num],
                                method = "spearman")

        f_healthy_results <- rbind(f_healthy_results, data.frame(
          题目 = f_var,
          简单相关系数 = round(simple_cor$estimate, 3),
          简单相关p值 = round(simple_cor$p.value, 3),
          偏相关系数 = round(partial_cor$estimate, 3),
          偏相关p值 = round(partial_cor$p.value, 3),
          stringsAsFactors = FALSE
        ))
      }, error = function(e) {
        cat(sprintf("警告: %s 与healthy偏相关分析失败: %s\n", f_var, e$message))
      })
    }
  }
}

write.csv(f_healthy_results,
          "outputs/f_behavior_healthy_partial_correlation.csv",
          row.names = FALSE)

# 3.2 food_intake_freq总分与healthy的偏相关分析
cat("3.2 分析food_intake_freq总分与healthy的偏相关性...\n")

if ("food_intake_freq_total" %in% names(data_numeric) &&
    "healthy_binary" %in% names(data_numeric)) {

  analysis_vars <- c("food_intake_freq_total", "healthy_binary",
                     control_vars_num)
  clean_data <- data_numeric %>%
    select(all_of(analysis_vars)) %>%
    na.omit()

  if (nrow(clean_data) > 10) {
    simple_cor <- cor.test(clean_data$food_intake_freq_total,
                          clean_data$healthy_binary,
                          method = "spearman")

    tryCatch({
      partial_cor <- pcor.test(clean_data$food_intake_freq_total,
                              clean_data$healthy_binary,
                              clean_data[, control_vars_num],
                              method = "spearman")

      total_healthy_results <- data.frame(
        变量 = "food_intake_freq_total",
        简单相关系数 = round(simple_cor$estimate, 3),
        简单相关p值 = round(simple_cor$p.value, 3),
        偏相关系数 = round(partial_cor$estimate, 3),
        偏相关p值 = round(partial_cor$p.value, 3),
        stringsAsFactors = FALSE
      )

      write.csv(total_healthy_results,
                "outputs/freq_total_healthy_partial_correlation.csv",
                row.names = FALSE)

      cat(sprintf("food_intake_freq总分与healthy:\n"))
      cat(sprintf("  简单相关系数: %.3f (p=%.3f)\n",
                  simple_cor$estimate, simple_cor$p.value))
      cat(sprintf("  偏相关系数: %.3f (p=%.3f)\n",
                  partial_cor$estimate, partial_cor$p.value))
    }, error = function(e) {
      cat(sprintf("警告: food_intake_freq总分与healthy偏相关分析失败: %s\n",
                  e$message))
    })
  }
}

# 生成分析报告摘要
cat("\n=== 健康行为分析完成摘要 ===\n")
cat("1. 健康行为描述性分析已完成\n")
cat("   - F1-14及G1-4各题频次分析（计数及占比）\n")
cat("   - food_intake_freq总分分布分析（正态性检验）\n")
cat("   - 各组间分布差异性分析\n")
cat("2. 营养素养与健康行为偏相关分析已完成\n")
cat("   - 营养素养与F1-14各题偏相关性\n")
cat("   - 营养素养与food_intake_freq总分偏相关性\n")
cat("   - 营养素养与二分类行为变量偏相关性\n")
cat("3. 健康行为与healthy偏相关分析已完成\n")
cat("   - F1-14各题与healthy偏相关性\n")
cat("   - food_intake_freq总分与healthy偏相关性\n")
cat("\n所有结果已保存至outputs文件夹\n")

cat("数据分析完成！\n")
