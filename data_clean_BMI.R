# 加载必要的库
library(readxl)
library(dplyr)
library(purrr)
library(stringr)
library(tidyr)

# 1. 读取BMI文件夹中所有Excel文件

cat("正在检索BMI文件夹...\n")
bmi_folder <- "BMI"

# 检查文件夹是否存在
if (!dir.exists(bmi_folder)) {
  stop("BMI文件夹不存在！请检查路径: ", bmi_folder)
}

# 获取文件夹中所有Excel文件
excel_files <- list.files(path = bmi_folder, 
                          pattern = "\\.(xlsx|xls)$", 
                          full.names = TRUE)

if (length(excel_files) == 0) {
  stop("BMI文件夹中未找到Excel文件")
}

cat(sprintf("已找到%d个Excel文件\n", length(excel_files)))

# 2. 读取并合并所有文件和sheet的数据
all_data_list <- list()
file_index <- 1

for (file_path in excel_files) {
  cat(sprintf("处理文件(%d/%d): %s\n", file_index, length(excel_files), basename(file_path)))
  
  # 获取所有sheet
  sheets <- excel_sheets(file_path)
  
  for (sheet in sheets) {
    cat(sprintf("  - 读取sheet: %s\n", sheet))
    
    tryCatch({
      # 读取当前sheet的数据
      sheet_data <- read_excel(file_path, sheet = sheet)
      
      # 如果sheet有数据，则处理
      if (nrow(sheet_data) > 0) {
        # 添加源文件和sheet信息
        sheet_data$source_file <- basename(file_path)
        sheet_data$source_sheet <- sheet
        
        # 添加到列表
        all_data_list[[length(all_data_list) + 1]] <- sheet_data
      }
    }, error = function(e) {
      cat(sprintf("    警告: 读取失败: %s\n", e$message))
    })
  }
  
  file_index <- file_index + 1
}

cat(sprintf("共读取了%d个sheet的数据\n", length(all_data_list)))

# 3. 提取关键字段
cat("正在提取关键字段...\n")

extract_data <- function(data_frame) {
  # 定义关键字段的匹配模式
  field_patterns <- list(
    学校名称 = c("学校", "校名", "校区", "school"),
    年级 = c("年级", "级别", "grade"),
    班级 = c("班级", "班", "class"),
    学生姓名 = c("姓名", "名字", "学生", "name"),
    出生日期 = c("出生日期", "生日", "birthday"),
    身高 = c("身高", "高度", "height"),
    体重 = c("体重", "重量", "weight")
  )
  
  # 创建结果数据框
  result <- data.frame(
    学校名称 = character(nrow(data_frame)),
    年级 = character(nrow(data_frame)),
    班级 = character(nrow(data_frame)),
    学生姓名 = character(nrow(data_frame)),
    身高 = numeric(nrow(data_frame)),
    体重 = numeric(nrow(data_frame)),
    stringsAsFactors = FALSE
  )
  
  # 对每个字段，查找匹配的列
  for (field_name in names(field_patterns)) {
    patterns <- field_patterns[[field_name]]
    
    # 查找匹配的列名
    matched_cols <- NULL
    for (pattern in patterns) {
      matches <- grep(pattern, colnames(data_frame), ignore.case = TRUE)
      if (length(matches) > 0) {
        matched_cols <- c(matched_cols, matches)
      }
    }
    
    # 如果找到匹配的列，提取数据
    if (length(matched_cols) > 0) {
      # 使用第一个匹配的列
      first_match <- matched_cols[1]
      col_data <- data_frame[[first_match]]
      
      # 根据字段类型进行转换
      if (field_name %in% c("身高", "体重")) {
        # 尝试转换为数值
        col_data <- as.numeric(as.character(col_data))
      } else {
        # 字符型字段
        col_data <- as.character(col_data)
      }
      
      # 存储数据
      result[[field_name]] <- col_data
    }
  }
  
  return(result)
}

# 应用函数到每个数据框并合并
extracted_data_list <- lapply(all_data_list, extract_data)
data <- bind_rows(extracted_data_list)





merged_data <- data %>%
  mutate(
    出生日期 = as.Date(出生日期, format = "%Y-%m-%d"),
    年龄 = round(as.numeric(as.Date("2024-12-31", format = "%Y-%m-%d") - 出生日期)/365.25,0)
  ) %>%
  select(学校名称, 年级, 班级, 学生姓名, 出生日期, 年龄, 身高, 体重)



# 4. 处理学校名称、年级字段

merged_data$学校名称 <- case_when(
  grepl("师", merged_data$学校名称) ~ "北师大附小",
  grepl("海岸", merged_data$学校名称) ~ "海岸小学", 
  grepl("丽湖", merged_data$学校名称) ~ "丽湖学校",
  grepl("荔林", merged_data$学校名称) ~ "荔林小学",
  grepl("荔湾", merged_data$学校名称) ~ "荔湾小学",
  grepl("二外|海德", merged_data$学校名称) ~ "南山二外",
  grepl("南山小", merged_data$学校名称) ~ "南山小学",
  grepl("南头小", merged_data$学校名称) ~ "南头小学",
  grepl("港湾", merged_data$学校名称) ~ "前海港湾小学",
  grepl("前海", merged_data$学校名称) ~ "前海小学",
  grepl("山海", merged_data$学校名称) ~ "山海学校",
  grepl("蛇口学校|蛇口小学", merged_data$学校名称) ~ "蛇口学校",
  grepl("深湾", merged_data$学校名称) ~ "深湾小学",
  grepl("松坪", merged_data$学校名称) ~ "松坪学校",
  grepl("太子湾", merged_data$学校名称) ~ "太子湾小学",
  grepl("文理二", merged_data$学校名称) ~ "文理二小",
  grepl("文理一", merged_data$学校名称) ~ "文理一小",
  grepl("向南", merged_data$学校名称) ~ "向南小学",
  grepl("育才三小", merged_data$学校名称) ~ "育才三小",
  grepl("园丁", merged_data$学校名称) ~ "园丁学校",
  grepl("珠光", merged_data$学校名称) ~ "珠光小学",
  TRUE ~ merged_data$学校名称
)

cat("正在标准化年级字段...\n")
merged_data$年级 <- case_when(
  grepl("三", merged_data$年级) ~ 3,
  grepl("四", merged_data$年级) ~ 4,
  grepl("五", merged_data$年级) ~ 5,
  TRUE ~ as.numeric(merged_data$年级)
)

# 提取三四五年级数据
merged_data <- merged_data %>%
  filter(年级 %in% c(3, 4, 5))

# original_data <- merged_data %>%
#   filter(学校名称 %in% c("太子湾小学", "育才三小")) %>%
#   select(学校名称, 年级, 班级, 身高, 体重)

# print(original_data)

# 5. 清理数据
cat("正在清理数据...\n")

# 移除完全空行
# merged_data <- merged_data %>%
#   filter(!(is.na(学生姓名) & is.na(身高) & is.na(体重)))

# 6. 保存结果
cat("正在保存处理后的数据...\n")
if (!dir.exists("outputs")) {
  dir.create("outputs")
}

write.csv(merged_data, "outputs/processed_data_bmi.csv", row.names = FALSE)

cat("BMI数据处理完成！共整合了", nrow(merged_data), "条记录\n")
