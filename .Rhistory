height = 8,
dpi = 300)
# 4.3 儿童营养素养与照料者营养素养的偏相关分析
library(ppcor)
# 创建偏相关分析函数
partial_correlation_analysis <- function(data, child_var, parent_var) {
# 准备数据
analysis_data <- data %>%
select(
child_score = !!sym(child_var),
parent_score = !!sym(parent_var),
年级, 性别, 家庭经济条件
) %>%
# 将分类变量转换为数值
mutate(
年级_num = as.numeric(年级),
性别_num = as.numeric(性别),
经济条件_num = as.numeric(家庭经济条件)
) %>%
select(child_score, parent_score, 年级_num, 性别_num, 经济条件_num)
# 执行偏相关分析
pcor_test <- pcor.test(
x = analysis_data$child_score,
y = analysis_data$parent_score,
z = analysis_data[, c("年级_num", "性别_num", "经济条件_num")],
method = "spearman"
)
# 整理结果
results <- data.frame(
儿童维度 = child_var,
照料者维度 = parent_var,
偏相关系数 = round(pcor_test$estimate, 3),
p值 = round(pcor_test$p.value, 3)
)
return(results)
}
# 定义父母维度
parent_vars <- c(
"Knowledge_Concepts_parent",
"Food_Selection_parent",
"Food_Preparation_parent",
"Food_Consumption_parent",
"Domain_Cognition_parent",
"Domain_Skills_parent",
"Total_Core_Information_parent"
)
# 对每个维度进行偏相关分析
all_partial_correlation_results <- map2_dfr(
stats_columns,
parent_vars,
~partial_correlation_analysis(all_data, .x, .y)
)
# 对每个维度进行偏相关分析
all_partial_correlation_results <- map2_dfr(
stats_columns,
parent_vars,
~partial_correlation_analysis(all_data, .x, .y)
)
# 创建偏相关分析函数
partial_correlation_analysis <- function(data, child_var, parent_var) {
# 准备数据
analysis_data <- data %>%
select(
child_score = !!sym(child_var),
parent_score = !!sym(parent_var),
年级, 性别, 家庭经济条件
) %>%
# 将分类变量转换为数值
mutate(
年级_num = as.numeric(年级),
性别_num = as.numeric(性别),
经济条件_num = as.numeric(家庭经济条件)
) %>%
select(child_score, parent_score, 年级_num, 性别_num, 经济条件_num)
# 执行偏相关分析
pcor_test <- pcor.test(
x = analysis_data$child_score,
y = analysis_data$parent_score,
z = analysis_data[, c("年级_num", "性别_num", "经济条件_num")],
method = "spearman"
)
# 整理结果
results <- data.frame(
儿童维度 = child_var,
照料者维度 = parent_var,
偏相关系数 = round(pcor_test$estimate, 3),
p值 = round(pcor_test$p.value, 3)
)
return(results)
}
# 创建偏相关分析函数
partial_correlation_analysis <- function(data, child_var, parent_var) {
# 准备数据
analysis_data <- data %>%
select(
child_score = !!sym(child_var),
parent_score = !!sym(parent_var),
年级, 性别, 家庭经济条件
) %>%
# 将分类变量转换为数值
mutate(
年级_num = as.numeric(年级),
性别_num = as.numeric(性别),
经济条件_num = as.numeric(家庭经济条件)
) %>%
select("child_score", "parent_score", "年级_num", "性别_num", "经济条件_num")
# 移除含有缺失值的行
analysis_data <- na.omit(analysis_data)
# 执行偏相关分析
pcor_test <- pcor.test(
x = analysis_data$child_score,
y = analysis_data$parent_score,
z = analysis_data[, c("年级_num", "性别_num", "经济条件_num")],
method = "spearman"
)
# 整理结果
results <- data.frame(
儿童维度 = child_var,
照料者维度 = parent_var,
偏相关系数 = round(pcor_test$estimate, 3),
p值 = round(pcor_test$p.value, 3)
)
return(results)
}
# 定义父母维度
parent_vars <- c(
"Knowledge_Concepts_parent",
"Food_Selection_parent",
"Food_Preparation_parent",
"Food_Consumption_parent",
"Domain_Cognition_parent",
"Domain_Skills_parent",
"Total_Core_Information_parent"
)
# 对每个维度进行偏相关分析
all_partial_correlation_results <- map2_dfr(
stats_columns,
parent_vars,
~partial_correlation_analysis(all_data, .x, .y)
)
# 创建偏相关分析函数
partial_correlation_analysis <- function(data, child_var, parent_var) {
# 准备数据
analysis_data <- data %>%
select(
child_score = !!sym(child_var),
parent_score = !!sym(parent_var),
年级, 性别, 家庭经济条件
) %>%
# 将分类变量转换为数值
mutate(
年级_num = as.numeric(as.factor(年级)),
性别_num = as.numeric(as.factor(性别)),
经济条件_num = as.numeric(as.factor(家庭经济条件))
) %>%
select(child_score, parent_score, 年级_num, 性别_num, 经济条件_num)
# 移除含有缺失值的行
analysis_data <- na.omit(analysis_data)
# 执行偏相关分析
pcor_test <- pcor.test(
x = analysis_data$child_score,
y = analysis_data$parent_score,
z = analysis_data[, c("年级_num", "性别_num", "经济条件_num")],
method = "spearman"
)
# 整理结果
results <- data.frame(
儿童维度 = child_var,
照料者维度 = parent_var,
偏相关系数 = round(pcor_test$estimate, 3),
p值 = round(pcor_test$p.value, 3)
)
return(results)
}
# 定义父母维度
parent_vars <- c(
"Knowledge_Concepts_parent",
"Food_Selection_parent",
"Food_Preparation_parent",
"Food_Consumption_parent",
"Domain_Cognition_parent",
"Domain_Skills_parent",
"Total_Core_Information_parent"
)
# 对每个维度进行偏相关分析
all_partial_correlation_results <- map2_dfr(
stats_columns,
parent_vars,
~partial_correlation_analysis(all_data, .x, .y)
)
# 4.3 儿童营养素养与照料者营养素养的偏相关分析
library(ppcor)
# 创建偏相关分析函数
partial_correlation_analysis <- function(data, child_var, parent_var) {
# 准备数据
analysis_data <- data %>%
dplyr::select(
child_score = !!sym(child_var),
parent_score = !!sym(parent_var),
年级, 性别, 家庭经济条件
) %>%
# 将分类变量转换为数值
mutate(
年级_num = as.numeric(年级),
性别_num = as.numeric(性别),
经济条件_num = as.numeric(家庭经济条件)
) %>%
dplyr::select(child_score, parent_score, 年级_num, 性别_num, 经济条件_num)
# 移除含有缺失值的行
analysis_data <- na.omit(analysis_data)
# 执行偏相关分析
pcor_test <- pcor.test(
x = analysis_data$child_score,
y = analysis_data$parent_score,
z = analysis_data[, c("年级_num", "性别_num", "经济条件_num")],
method = "spearman"
)
# 整理结果
results <- data.frame(
儿童维度 = child_var,
照料者维度 = parent_var,
偏相关系数 = round(pcor_test$estimate, 3),
p值 = round(pcor_test$p.value, 3)
)
return(results)
}
# 定义父母维度
parent_vars <- c(
"Knowledge_Concepts_parent",
"Food_Selection_parent",
"Food_Preparation_parent",
"Food_Consumption_parent",
"Domain_Cognition_parent",
"Domain_Skills_parent",
"Total_Core_Information_parent"
)
# 对每个维度进行偏相关分析
all_partial_correlation_results <- map2_dfr(
stats_columns,
parent_vars,
~partial_correlation_analysis(all_data, .x, .y)
)
library(tidyverse)
# 4.3 儿童营养素养与照料者营养素养的偏相关分析
library(ppcor)
# 创建偏相关分析函数
partial_correlation_analysis <- function(data, child_var, parent_var) {
# 准备数据
analysis_data <- data %>%
dplyr::select(
child_score = !!sym(child_var),
parent_score = !!sym(parent_var),
年级, 性别, 家庭经济条件
) %>%
# 将分类变量转换为数值
mutate(
年级_num = as.numeric(年级),
性别_num = as.numeric(性别),
经济条件_num = as.numeric(家庭经济条件)
) %>%
dplyr::select(child_score, parent_score, 年级_num, 性别_num, 经济条件_num)
# 移除含有缺失值的行
analysis_data <- na.omit(analysis_data)
# 执行偏相关分析
pcor_test <- pcor.test(
x = analysis_data$child_score,
y = analysis_data$parent_score,
z = analysis_data[, c("年级_num", "性别_num", "经济条件_num")],
method = "spearman"
)
# 整理结果
results <- data.frame(
儿童维度 = child_var,
照料者维度 = parent_var,
偏相关系数 = round(pcor_test$estimate, 3),
p值 = round(pcor_test$p.value, 3)
)
return(results)
}
# 定义父母维度
parent_vars <- c(
"Knowledge_Concepts_parent",
"Food_Selection_parent",
"Food_Preparation_parent",
"Food_Consumption_parent",
"Domain_Cognition_parent",
"Domain_Skills_parent",
"Total_Core_Information_parent"
)
# 对每个维度进行偏相关分析
all_partial_correlation_results <- map2_dfr(
stats_columns,
parent_vars,
~partial_correlation_analysis(all_data, .x, .y)
)
# 保存结果
write.csv(all_partial_correlation_results,
"outputs/partial_correlation_analysis_results_parent.csv",
row.names = FALSE)
# 创建偏相关性热图
library(ggplot2)
library(reshape2)
# 准备相关性矩阵数据
pcor_matrix <- matrix(NA,
nrow = length(stats_columns),
ncol = length(parent_vars))
for(i in seq_along(stats_columns)) {
for(j in seq_along(parent_vars)) {
pcor_result <- partial_correlation_analysis(all_data, stats_columns[i], parent_vars[j])
pcor_matrix[i,j] <- pcor_result$偏相关系数
}
# 转换维度名称为中文
dimension_names <- c("营养相关知识理念", "选择食物", "制作食物",
"摄入食物", "认知领域", "技能领域", "营养素养总分")
# 创建热图
pcor_melted <- melt(pcor_matrix)
pcor_plot <- ggplot(pcor_melted, aes(x = Var1, y = Var2, fill = value)) +
geom_tile() +
scale_fill_gradient2(low = "blue", high = "red", mid = "white",
midpoint = 0, limit = c(-1,1), name = "偏相关系数") +
geom_text(aes(label = sprintf("%.2f", value)), color = "black", size = 3) +
scale_x_discrete(labels = dimension_names) +
scale_y_discrete(labels = dimension_names) +
labs(x = "儿童营养素养维度",
y = "照料者营养素养维度",
title = "儿童-照料者营养素养偏相关性热图\n(校正年龄、性别和家庭经济条件)") +
theme_minimal() +
theme(axis.text.x = element_text(angle = 45, hjust = 1),
plot.title = element_text(hjust = 0.5))
# 保存热图
ggsave("outputs/parent_child_partial_correlation_heatmap.png",
pcor_plot,
width = 12,
height = 8,
dpi = 300)
pcor_plot <- ggplot(pcor_melted, aes(x = Var1, y = Var2, fill = value)) +
geom_tile() +
scale_fill_gradient2(low = "blue", high = "red", mid = "white",
midpoint = 0, limit = c(-1,1), name = "偏相关系数") +
geom_text(aes(label = sprintf("%.2f", value)), color = "black", size = 3) +
scale_x_discrete(labels = parent_names) +
scale_y_discrete(labels = child_names) +
labs(x = "儿童营养素养维度",
y = "照料者营养素养维度",
title = "儿童-照料者营养素养偏相关性热图\n(校正年龄、性别和家庭经济条件)") +
theme_minimal() +
theme(axis.text.x = element_text(angle = 45, hjust = 1),
plot.title = element_text(hjust = 0.5))
# 保存热图
ggsave("outputs/parent_child_partial_correlation_heatmap.png",
pcor_plot,
width = 12,
height = 8,
dpi = 300)
pcor_plot <- ggplot(pcor_melted, aes(x = Var1, y = Var2, fill = value)) +
geom_tile() +
scale_fill_gradient2(low = "blue", high = "red", mid = "white",
midpoint = 0, limit = c(-1,1), name = "偏相关系数") +
geom_text(aes(label = sprintf("%.2f", value)), color = "black", size = 3) +
scale_x_discrete(labels = child_names) +
scale_y_discrete(labels = parent_names) +
labs(x = "儿童营养素养维度",
y = "照料者营养素养维度",
title = "儿童-照料者营养素养偏相关性热图\n(校正年龄、性别和家庭经济条件)") +
theme_minimal() +
theme(axis.text.x = element_text(angle = 45, hjust = 1),
plot.title = element_text(hjust = 0.5))
pcor_plot
pcor_melted <- melt(pcor_matrix)
pcor_plot <- ggplot(pcor_melted, aes(x = Var1, y = Var2, fill = value)) +
geom_tile() +
scale_fill_gradient2(low = "blue", high = "red", mid = "white",
midpoint = 0, limit = c(-1,1), name = "偏相关系数") +
geom_text(aes(label = sprintf("%.2f", value)), color = "black", size = 3) +
scale_x_discrete(labels = child_names) +
scale_y_discrete(labels = parent_names) +
labs(x = "儿童营养素养维度",
y = "照料者营养素养维度",
title = "儿童-照料者营养素养偏相关性热图\n(校正年龄、性别和家庭经济条件)") +
theme_minimal() +
theme(axis.text.x = element_text(angle = 45, hjust = 1),
plot.title = element_text(hjust = 0.5))
pcor_plot
# 4.3 儿童营养素养与照料者营养素养的偏相关分析
library(ppcor)
# 创建偏相关分析函数
partial_correlation_analysis <- function(data, child_var, parent_var) {
# 准备数据
analysis_data <- data %>%
dplyr::select(
child_score = !!sym(child_var),
parent_score = !!sym(parent_var),
年级, 性别, 家庭经济条件
) %>%
# 将分类变量转换为数值
mutate(
年级_num = as.numeric(年级),
性别_num = as.numeric(性别),
经济条件_num = as.numeric(家庭经济条件)
) %>%
dplyr::select(child_score, parent_score, 年级_num, 性别_num, 经济条件_num)
# 移除含有缺失值的行
analysis_data <- na.omit(analysis_data)
# 执行偏相关分析
pcor_test <- pcor.test(
x = analysis_data$child_score,
y = analysis_data$parent_score,
z = analysis_data[, c("年级_num", "性别_num", "经济条件_num")],
method = "spearman"
)
# 整理结果
results <- data.frame(
儿童维度 = child_var,
照料者维度 = parent_var,
偏相关系数 = round(pcor_test$estimate, 3),
p值 = round(pcor_test$p.value, 3)
)
return(results)
}
# 定义父母维度
parent_vars <- c(
"Knowledge_Concepts_parent",
"Food_Selection_parent",
"Food_Preparation_parent",
"Food_Consumption_parent",
"Domain_Cognition_parent",
"Domain_Skills_parent",
"Total_Core_Information_parent"
)
# 对每个维度进行偏相关分析
all_partial_correlation_results <- map2_dfr(
stats_columns,
parent_vars,
~partial_correlation_analysis(all_data, .x, .y)
)
# 保存结果
write.csv(all_partial_correlation_results,
"outputs/partial_correlation_analysis_results_parent.csv",
row.names = FALSE)
# 创建偏相关性热图
library(ggplot2)
library(reshape2)
# 准备相关性矩阵数据
pcor_matrix <- matrix(NA,
nrow = length(stats_columns),
ncol = length(parent_vars))
for(i in seq_along(stats_columns)) {
for(j in seq_along(parent_vars)) {
pcor_result <- partial_correlation_analysis(all_data, stats_columns[i], parent_vars[j])
pcor_matrix[i,j] <- pcor_result$偏相关系数
}
# 转换维度名称为中文
# dimension_names <- c("营养相关知识理念", "选择食物", "制作食物",
# "摄入食物", "认知领域", "技能领域", "营养素养总分")
# 创建热图
pcor_melted <- melt(pcor_matrix)
pcor_plot <- ggplot(pcor_melted, aes(x = Var1, y = Var2, fill = value)) +
geom_tile() +
scale_fill_gradient2(low = "blue", high = "red", mid = "white",
midpoint = 0, limit = c(-1,1), name = "偏相关系数") +
geom_text(aes(label = sprintf("%.2f", value)), color = "black", size = 3) +
scale_x_discrete(labels = child_names) +
scale_y_discrete(labels = parent_names) +
labs(x = "儿童营养素养维度",
y = "照料者营养素养维度",
title = "儿童-照料者营养素养偏相关性热图\n(校正年龄、性别和家庭经济条件)") +
theme_minimal() +
theme(axis.text.x = element_text(angle = 45, hjust = 1),
plot.title = element_text(hjust = 0.5))
# 保存热图
ggsave("outputs/parent_child_partial_correlation_heatmap.png",
pcor_plot,
width = 12,
height = 8,
dpi = 300)
pcor_plot <- ggplot(pcor_melted, aes(x = Var1, y = Var2, fill = value)) +
geom_tile() +
scale_fill_gradient2(low = "blue", high = "red", mid = "white",
midpoint = 0, limit = c(-1,1), name = "偏相关系数") +
geom_text(aes(label = sprintf("%.2f", value)), color = "black", size = 3) +
scale_x_discrete(labels = child_names, name = "儿童营养素养维度") +
scale_y_discrete(labels = parent_names, name = "照料者营养素养维度") +
labs(title = "儿童-照料者营养素养偏相关性热图\n(校正年龄、性别和家庭经济条件)") +
theme_minimal() +
theme(axis.text.x = element_text(angle = 45, hjust = 1),
plot.title = element_text(hjust = 0.5))
# 保存热图
ggsave("outputs/parent_child_partial_correlation_heatmap.png",
pcor_plot,
width = 12,
height = 8,
dpi = 300)
pcor_plot <- ggplot(pcor_melted, aes(x = Var1, y = Var2, fill = value)) +
geom_tile() +
scale_fill_gradient2(low = "blue", high = "red", mid = "white",
midpoint = 0, limit = c(-1,1), name = "偏相关系数") +
geom_text(aes(label = sprintf("%.2f", value)), color = "black", size = 3) +
scale_x_discrete(labels = child_names) +
scale_y_discrete(labels = parent_names) +
labs(x = "儿童营养素养维度",
y = "照料者营养素养维度",
title = "儿童-照料者营养素养偏相关性热图\n(校正年龄、性别和家庭经济条件)") +
theme_minimal() +
theme(axis.text.x = element_text(angle = 45, hjust = 1),
plot.title = element_text(hjust = 0.5))
# 保存热图
ggsave("outputs/parent_child_partial_correlation_heatmap.png",
pcor_plot,
width = 12,
height = 8,
dpi = 300)
pcor_plot
