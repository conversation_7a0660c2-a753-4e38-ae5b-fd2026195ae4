# 简化版健康行为数据分析
# 避免复杂包依赖，专注于核心分析

# 加载基础库
library(readxl)
library(tidyverse)

cat("开始数据分析...\n")

# 1. 数据读取和预处理 ---------------------------------------------------------------

kid_data <- read.csv("processed_data.csv")
bmi_data <- read.csv("processed_data_bmi.csv")
parent_data <- read.csv("processed_data_parents.csv")

# 合并数据
data <- kid_data %>%
  left_join(bmi_data %>% select(学校名称, 年级, 班级, 学生姓名, 年龄),
            by = c("学校名称", "年级", "班级", "学生姓名"),
            suffix = c("", "_bmi")) %>%
  left_join(parent_data,
            by = c("学校名称" = "school",
                   "年级" = "Kidgrade", 
                   "学生姓名" = "Kidname"),
            suffix = c("", "_parent")) %>%
  select(-ends_with("_bmi"), -ends_with(".y"))

cat("数据合并完成！\n")

# 读取BMI标准并计算健康状况
bmi_standard <- readxl::read_excel("BMI判断标准.xls")

data <- data %>%
  mutate(
    身高 = ifelse(身高 > 2, 身高 / 100, 身高),
    体重 = ifelse(体重 >= 200, 体重 / 2, 体重),
    BMI = round(体重 / (身高^2), 1)
  ) %>%
  left_join(bmi_standard, by = c("性别", "年龄")) %>%
  mutate(
    healthy = if_else(BMI > 消瘦标准 & BMI < 超重标准, "正常", "异常", 
                      missing = NA_character_),
    overweight = case_when(
      BMI >= 超重标准 ~ "是",
      is.na(BMI) ~ NA_character_,
      TRUE ~ "否"
    )
  )

# 行为变量转换
data <- data %>%
  mutate(
    # E1-E4转换
    food_intake_type1 = case_when(E1 == 1 ~ 1, E1 == 2 ~ 0, TRUE ~ NA_real_),
    food_intake_type2 = case_when(E2 == 1 ~ 1, E2 == 2 ~ 0, TRUE ~ NA_real_),
    food_intake_type3 = case_when(E3 == 1 ~ 1, E3 == 2 ~ 0, TRUE ~ NA_real_),
    food_intake_type4 = case_when(E4 == 1 ~ 1, E4 == 2 ~ 0, TRUE ~ NA_real_),
    
    # F1-F14转换（F8、F12-F14反向计分）
    food_intake_freq1 = F1,
    food_intake_freq2 = F2,
    food_intake_freq3 = F3,
    food_intake_freq4 = F4,
    food_intake_freq5 = F5,
    food_intake_freq6 = F6,
    food_intake_freq7 = F7,
    food_intake_freq8 = case_when(
      F8 == 1 ~ 5, F8 == 2 ~ 4, F8 == 3 ~ 3, F8 == 4 ~ 2, F8 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq9 = F9,
    food_intake_freq10 = F10,
    food_intake_freq11 = F11,
    food_intake_freq12 = case_when(
      F12 == 1 ~ 5, F12 == 2 ~ 4, F12 == 3 ~ 3, F12 == 4 ~ 2, F12 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq13 = case_when(
      F13 == 1 ~ 5, F13 == 2 ~ 4, F13 == 3 ~ 3, F13 == 4 ~ 2, F13 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    food_intake_freq14 = case_when(
      F14 == 1 ~ 5, F14 == 2 ~ 4, F14 == 3 ~ 3, F14 == 4 ~ 2, F14 == 5 ~ 1,
      TRUE ~ NA_real_
    ),
    
    # G1-G4转换
    water_drink = case_when(
      G1 == 2 ~ 1, G1 %in% c(1, 3, 4) ~ 0, G1 == 5 ~ NA_real_, TRUE ~ NA_real_
    ),
    outdoor_excise = case_when(
      G2 %in% c(1, 2) ~ 0, G2 %in% c(3, 4, 5) ~ 1, TRUE ~ NA_real_
    ),
    nutrition_competition = case_when(
      G3 == 1 ~ 1, G3 == 2 ~ 0, TRUE ~ NA_real_
    ),
    weight_monitoring = case_when(
      G4 == 1 ~ 1, G4 == 2 ~ 0, TRUE ~ NA_real_
    )
  )

# 基本情况变量转换
data <- data %>%
  mutate(
    年级 = factor(case_when(
      年级 == 3 ~ "3年级", 年级 == 4 ~ "4年级", 年级 == 5 ~ "5年级",
      TRUE ~ NA_character_
    ), levels = c("3年级", "4年级", "5年级")),
    性别 = factor(case_when(
      性别 == 1 ~ "男", 性别 == 2 ~ "女", TRUE ~ NA_character_
    ), levels = c("男", "女")),
    主要照料者 = factor(case_when(
      A1 == 1 ~ "父母", A1 == 2 ~ "祖辈", A1 == 3 ~ "其他", TRUE ~ NA_character_
    ), levels = c("父母", "祖辈", "其他")),
    主要照料者学历 = factor(case_when(
      A2 == 1 ~ "小学及以下", A2 == 2 ~ "初中", A2 == 3 ~ "高中", 
      A2 == 4 ~ "大专", A2 == 5 ~ "本科及以上", TRUE ~ NA_character_
    ), levels = c("小学及以下", "初中", "高中", "大专", "本科及以上")),
    家庭经济条件 = factor(case_when(
      A3 == 2 ~ "低", A3 == 1 & A4 <= 2 ~ "中", A3 == 1 & A4 > 2 ~ "高",
      TRUE ~ NA_character_
    ), levels = c("低", "中", "高"))
  )

# 计算总分和二分类变量
data <- data %>%
  mutate(
    food_intake_freq_total = rowSums(
      select(., food_intake_freq1:food_intake_freq14), na.rm = TRUE
    ),
    healthy_binary = case_when(
      healthy == "正常" ~ 1, healthy == "异常" ~ 0, TRUE ~ NA_real_
    )
  )

# 保存处理后的数据
write.csv(data, "outputs/cleaned_data_NL&behavior.csv", row.names = FALSE)
cat("数据预处理完成！\n")

# 2. 描述性分析 -----------------------------------------------------------------------

cat("开始描述性分析...\n")

# 创建输出目录
if (!dir.exists("outputs")) {
  dir.create("outputs")
}

f_questions <- paste0("F", 1:14)
g_questions <- paste0("G", 1:4)

# F1-14频次分析
cat("分析F1-14各题频次...\n")
f_freq_results <- list()
for (f_var in f_questions) {
  if (f_var %in% names(data)) {
    freq_table <- data %>%
      count(.data[[f_var]], name = "计数") %>%
      mutate(
        题目 = f_var,
        选项 = .data[[f_var]],
        占比 = round(计数 / sum(计数) * 100, 2)
      ) %>%
      select(题目, 选项, 计数, 占比)
    f_freq_results[[f_var]] <- freq_table
  }
}
f_all_freq <- do.call(rbind, f_freq_results)
write.csv(f_all_freq, "outputs/f_questions_frequency.csv", row.names = FALSE)

# G1-4频次分析
cat("分析G1-4各题频次...\n")
g_freq_results <- list()
for (g_var in g_questions) {
  if (g_var %in% names(data)) {
    freq_table <- data %>%
      count(.data[[g_var]], name = "计数") %>%
      mutate(
        题目 = g_var,
        选项 = .data[[g_var]],
        占比 = round(计数 / sum(计数) * 100, 2)
      ) %>%
      select(题目, 选项, 计数, 占比)
    g_freq_results[[g_var]] <- freq_table
  }
}
g_all_freq <- do.call(rbind, g_freq_results)
write.csv(g_all_freq, "outputs/g_questions_frequency.csv", row.names = FALSE)

# food_intake_freq总分分布
cat("分析food_intake_freq总分分布...\n")
shapiro_test <- shapiro.test(data$food_intake_freq_total)
is_normal <- shapiro_test$p.value > 0.05

freq_total_stats <- data.frame(
  变量 = "food_intake_freq总分",
  样本量 = sum(!is.na(data$food_intake_freq_total)),
  正态性检验p值 = round(shapiro_test$p.value, 3),
  分布类型 = ifelse(is_normal, "正态分布", "非正态分布"),
  均值 = round(mean(data$food_intake_freq_total, na.rm = TRUE), 2),
  标准差 = round(sd(data$food_intake_freq_total, na.rm = TRUE), 2),
  中位数 = round(median(data$food_intake_freq_total, na.rm = TRUE), 2),
  四分位间距 = round(IQR(data$food_intake_freq_total, na.rm = TRUE), 2)
)

write.csv(freq_total_stats, "outputs/freq_total_distribution.csv", 
          row.names = FALSE)

cat(sprintf("food_intake_freq总分: %s (p=%.3f)\n", 
            ifelse(is_normal, "服从正态分布", "不服从正态分布"),
            shapiro_test$p.value))

cat("描述性分析完成！\n")
