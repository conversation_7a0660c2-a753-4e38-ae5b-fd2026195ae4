<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<style>body{background-color:white;}</style>


</head>
<body>
<div id="yopkihphuq" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;">
  <style>#yopkihphuq table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#yopkihphuq thead, #yopkihphuq tbody, #yopkihphuq tfoot, #yopkihphuq tr, #yopkihphuq td, #yopkihphuq th {
  border-style: none;
}

#yopkihphuq p {
  margin: 0;
  padding: 0;
}

#yopkihphuq .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#yopkihphuq .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#yopkihphuq .gt_title {
  color: #333333;
  font-size: 125%;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#yopkihphuq .gt_subtitle {
  color: #333333;
  font-size: 85%;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#yopkihphuq .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#yopkihphuq .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#yopkihphuq .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#yopkihphuq .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#yopkihphuq .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#yopkihphuq .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#yopkihphuq .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#yopkihphuq .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#yopkihphuq .gt_spanner_row {
  border-bottom-style: hidden;
}

#yopkihphuq .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#yopkihphuq .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#yopkihphuq .gt_from_md > :first-child {
  margin-top: 0;
}

#yopkihphuq .gt_from_md > :last-child {
  margin-bottom: 0;
}

#yopkihphuq .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#yopkihphuq .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#yopkihphuq .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#yopkihphuq .gt_row_group_first td {
  border-top-width: 2px;
}

#yopkihphuq .gt_row_group_first th {
  border-top-width: 2px;
}

#yopkihphuq .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#yopkihphuq .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#yopkihphuq .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#yopkihphuq .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#yopkihphuq .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#yopkihphuq .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#yopkihphuq .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#yopkihphuq .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#yopkihphuq .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#yopkihphuq .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#yopkihphuq .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#yopkihphuq .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#yopkihphuq .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#yopkihphuq .gt_left {
  text-align: left;
}

#yopkihphuq .gt_center {
  text-align: center;
}

#yopkihphuq .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#yopkihphuq .gt_font_normal {
  font-weight: normal;
}

#yopkihphuq .gt_font_bold {
  font-weight: bold;
}

#yopkihphuq .gt_font_italic {
  font-style: italic;
}

#yopkihphuq .gt_super {
  font-size: 65%;
}

#yopkihphuq .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#yopkihphuq .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#yopkihphuq .gt_indent_1 {
  text-indent: 5px;
}

#yopkihphuq .gt_indent_2 {
  text-indent: 10px;
}

#yopkihphuq .gt_indent_3 {
  text-indent: 15px;
}

#yopkihphuq .gt_indent_4 {
  text-indent: 20px;
}

#yopkihphuq .gt_indent_5 {
  text-indent: 25px;
}
</style>
  <table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false">
  <!--/html_preserve--><caption class='gt_caption'>基本情况分布</caption><!--html_preserve-->
  <thead>
    <tr class="gt_col_headings">
      <th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;变量&lt;/strong&gt;"><strong>变量</strong></th>
      <th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" scope="col" id="&lt;strong&gt;分布情况&lt;/strong&gt;&lt;span class=&quot;gt_footnote_marks&quot; style=&quot;white-space:nowrap;font-style:italic;font-weight:normal;&quot;&gt;&lt;sup&gt;1&lt;/sup&gt;&lt;/span&gt;"><strong>分布情况</strong><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span></th>
    </tr>
  </thead>
  <tbody class="gt_table_body">
    <tr><td headers="label" class="gt_row gt_left">年级</td>
<td headers="stat_0" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    3年级</td>
<td headers="stat_0" class="gt_row gt_center">824.0 (34.0%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    4年级</td>
<td headers="stat_0" class="gt_row gt_center">831.0 (34.3%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    5年级</td>
<td headers="stat_0" class="gt_row gt_center">768.0 (31.7%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">性别</td>
<td headers="stat_0" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    男</td>
<td headers="stat_0" class="gt_row gt_center">1,255.0 (51.8%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    女</td>
<td headers="stat_0" class="gt_row gt_center">1,168.0 (48.2%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">主要照料者</td>
<td headers="stat_0" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    父母</td>
<td headers="stat_0" class="gt_row gt_center">2,052.0 (84.7%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    祖辈</td>
<td headers="stat_0" class="gt_row gt_center">326.0 (13.5%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    其他</td>
<td headers="stat_0" class="gt_row gt_center">45.0 (1.9%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">主要照料者学历</td>
<td headers="stat_0" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    小学及以下</td>
<td headers="stat_0" class="gt_row gt_center">118.0 (4.9%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    初中</td>
<td headers="stat_0" class="gt_row gt_center">153.0 (6.3%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    高中</td>
<td headers="stat_0" class="gt_row gt_center">277.0 (11.4%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    大专</td>
<td headers="stat_0" class="gt_row gt_center">521.0 (21.5%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    本科及以上</td>
<td headers="stat_0" class="gt_row gt_center">1,354.0 (55.9%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">家庭经济条件</td>
<td headers="stat_0" class="gt_row gt_center"><br /></td></tr>
    <tr><td headers="label" class="gt_row gt_left">    低</td>
<td headers="stat_0" class="gt_row gt_center">866.0 (35.7%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    中</td>
<td headers="stat_0" class="gt_row gt_center">321.0 (13.2%)</td></tr>
    <tr><td headers="label" class="gt_row gt_left">    高</td>
<td headers="stat_0" class="gt_row gt_center">1,236.0 (51.0%)</td></tr>
  </tbody>
  
  <tfoot class="gt_footnotes">
    <tr>
      <td class="gt_footnote" colspan="2"><span class="gt_footnote_marks" style="white-space:nowrap;font-style:italic;font-weight:normal;"><sup>1</sup></span> n (%)</td>
    </tr>
  </tfoot>
</table>
</div>
</body>
</html>
