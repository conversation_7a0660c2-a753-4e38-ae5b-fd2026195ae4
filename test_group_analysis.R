# 测试F1-14和G1-4的组间差异分析
library(tidyverse)
library(gtsummary)

# 读取数据
data <- read.csv("outputs/cleaned_data_NL&behavior.csv")

# 定义变量
f_questions <- paste0("F", 1:14)
g_questions <- paste0("G", 1:4)
group_vars <- c("年级", "性别", "主要照料者", "主要照料者学历", "家庭经济条件")

cat("开始测试F1-14组间差异分析...\n")

# 测试F1-14的组间差异分析（转换为分类变量）
for (group_var in group_vars[1:2]) {  # 只测试前两个分组变量
  cat(sprintf("测试F1-14在%s组间差异...\n", group_var))
  
  tryCatch({
    f_result <- data %>%
      dplyr::select(all_of(c(f_questions, group_var))) %>%
      # 将F1-14转换为分类变量
      mutate(across(all_of(f_questions), ~ as.factor(.x))) %>%
      tbl_summary(
        by = group_var,
        statistic = list(all_categorical() ~ "{n} ({p}%)"),
        digits = everything() ~ 1,
        missing = "no"
      ) %>%
      add_p(
        test = list(all_categorical() ~ "chisq.test"),
        pvalue_fun = ~ style_pvalue(.x, digits = 3)
      ) %>%
      modify_header(label = "**题目**") %>%
      modify_spanning_header(all_stat_cols() ~ sprintf("**%s**", group_var))
    
    cat(sprintf("F1-14在%s组间差异分析成功！\n", group_var))
    
  }, error = function(e) {
    cat(sprintf("F1-14在%s组间差异分析失败: %s\n", group_var, e$message))
  })
}

cat("开始测试G1-4组间差异分析...\n")

# 测试G1-4的组间差异分析（转换为分类变量）
for (group_var in group_vars[1:2]) {  # 只测试前两个分组变量
  cat(sprintf("测试G1-4在%s组间差异...\n", group_var))
  
  tryCatch({
    g_result <- data %>%
      dplyr::select(all_of(c(g_questions, group_var))) %>%
      # 将G1-4转换为分类变量
      mutate(across(all_of(g_questions), ~ as.factor(.x))) %>%
      tbl_summary(
        by = group_var,
        statistic = list(all_categorical() ~ "{n} ({p}%)"),
        digits = everything() ~ 1,
        missing = "no"
      ) %>%
      add_p(
        test = list(all_categorical() ~ "chisq.test"),
        pvalue_fun = ~ style_pvalue(.x, digits = 3)
      ) %>%
      modify_header(label = "**题目**") %>%
      modify_spanning_header(all_stat_cols() ~ sprintf("**%s**", group_var))
    
    cat(sprintf("G1-4在%s组间差异分析成功！\n", group_var))
    
  }, error = function(e) {
    cat(sprintf("G1-4在%s组间差异分析失败: %s\n", group_var, e$message))
  })
}

cat("测试完成！\n")
