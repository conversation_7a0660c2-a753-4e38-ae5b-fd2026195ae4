# Part one: data combine ----------------------------------------------------------------------

# 加载必要的库
library(readxl)
library(tidyverse)
library(gtsummary)

# 1. 合并学生营养数据、家长营养数据及学生BMI数据

kid_data <- read.csv("outputs/processed_data.csv")
bmi_data <- read.csv("outputs/processed_data_bmi.csv")
parent_data <- read.csv("outputs/processed_data_parents.csv")

# 合并学生营养数据与BMI数据
data <- kid_data %>%
     left_join(bmi_data %>% select(学校名称, 年级, 班级, 学生姓名, 年龄),
        by = c("学校名称", "年级", "班级", "学生姓名"),
        suffix = c("", "_bmi")
    )  %>%
    # 合并家长营养数据
    left_join(
        parent_data,
        by = c(
            "学校名称" = "school",
            "年级" = "Kidgrade",
            "学生姓名" = "Kidname"
        ),
        suffix = c("", "_parent")
    ) %>%
    # 移除可能重复的列
    select(-ends_with("_bmi"), -ends_with(".y"))
cat("数据合并完成！\n")



# 2. 计算BMI并判断健康状况、超重肥胖及行为
cat("正在计算BMI并判断健康状况及超重肥胖...\n")

# 读取BMI判断标准
bmi_standard <- readxl::read_excel("BMI判断标准.xls")

# 处理身高体重单位并计算BMI
data <- data %>%
    mutate(
        # 转换身高单位（假设身高>2的单位是厘米，否则是米）
        身高 = ifelse(身高 > 2, 身高 / 100, 身高),
        # 转换体重单位（假设体重>=200的单位是斤，否则是公斤）
        体重 = ifelse(体重 >= 200, 体重 / 2, 体重),
        BMI = round(体重 / (身高^2), 1)
    ) %>%
    left_join(bmi_standard, by = c("性别", "年龄")) %>%
    mutate(
        healthy = if_else(BMI > 消瘦标准 & BMI< 超重标准, "正常", 
                          "异常", missing = NA_character_),
        overweight = case_when(
          BMI >= 超重标准 ~ "是",
          NA  ~ NA,
          TRUE ~ "否"
        )
    ) 

cat("正在计算行为...\n")
data <- data %>%
    mutate(
    # E1-E4题转换为食物摄入种类1-4（1为1，2为0，NA为NA）
    food_intake_type1 = case_when(
      E1 == 1 ~ 1,
      E1 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type2 = case_when(
      E2 == 1 ~ 1,
      E2 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type3 = case_when(
      E3 == 1 ~ 1,
      E3 == 2 ~ 0,
      TRUE ~ NA
    ),
    food_intake_type4 = case_when(
      E4 == 1 ~ 1,
      E4 == 2 ~ 0,
      TRUE ~ NA
    ),
    # F1-F14重命名为食物摄入频次1-14，
    food_intake_freq1 = F1,
    food_intake_freq2 = F2,
    food_intake_freq3 = F3,
    food_intake_freq4 = F4,
    food_intake_freq5 = F5,
    food_intake_freq6 = F6,
    food_intake_freq7 = F7,
    food_intake_freq8 = F8,
    food_intake_freq9 = F9,
    food_intake_freq10 = F10,
    food_intake_freq11 = F11,
    food_intake_freq12 = F12,
    food_intake_freq13 = F13,
    food_intake_freq14 = F14,
    # G1转为water_drink（选择2为1，选择1、3、4为0，选择5为NA）
    water_drink = case_when(
      G1 == 2 ~ 1,
      G1 %in% c(1, 3, 4) ~ 0,
      G1 == 5 ~ NA,
      TRUE ~ NA
    ),
    # G2转换为outdoor_excise（1、2为0，3、4、5为1，NA为NA）
    outdoor_excise = case_when(
      G2 %in% c(1, 2) ~ 0,
      G2 %in% c(3, 4, 5) ~ 1,
      TRUE ~ NA
    ),
    # G3转换为营养比赛（1为1，2为0，NA为NA）
    nutrition_competition = case_when(
      G3 == 1 ~ 1,
      G3 == 2 ~ 0,
      TRUE ~ NA
    ),
    # G4转换为体重监测（1为1，2为0，NA为NA）
    weight_monitoring = case_when(
      G4 == 1 ~ 1,
      G4 == 2 ~ 0,
      TRUE ~ NA
    )
)

cat("基本情况变量转换为等级变量...\n")
data <- data %>%
    mutate(
      年级 = factor(case_when(
        年级 == 3 ~ "3年级",
        年级 == 4 ~ "4年级",
        年级 == 5 ~ "5年级",
        TRUE ~ NA_character_
      ), levels = c("3年级", "4年级", "5年级")),
      性别 = factor(case_when(
        性别==1 ~ "男",
        性别==2 ~ "女",
        TRUE ~ NA_character_
      ),levels = c("男", "女")),
      主要照料者 = factor(case_when(
        A1 == 1 ~ "父母",
        A1 == 2 ~ "祖辈",
        A1 == 3 ~ "其他",
        TRUE ~ NA_character_
      ), levels = c("父母", "祖辈", "其他")),
      主要照料者学历 = factor(case_when(
        A2 == 1 ~ "小学及以下",
        A2 == 2 ~ "初中",
        A2 == 3 ~ "高中",
        A2 == 4 ~ "大专",
        A2 == 5 ~ "本科及以上",
        TRUE ~ NA_character_
      ), levels = c("小学及以下", "初中", "高中", "大专", "本科及以上")),
      家庭经济条件 = factor(case_when(
        A3 == 2 ~ "低",
        A3 == 1 & A4 <= 2 ~ "中",
        A3 == 1 & A4 > 2 ~ "高",
        TRUE ~ NA_character_
      ), levels = c("低", "中", "高"))
    )

# 保存处理后的数据
cat("保存处理后的数据...\n")
write.csv(all_data, "outputs/cleaned_data_NL&behavior.csv", row.names = FALSE)

cat("数据清洗完成！\n")


# Part two: data analysis ----------------------------------------------------------------------

