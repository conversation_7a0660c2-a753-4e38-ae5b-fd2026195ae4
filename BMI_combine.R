# 加载必要的库
library(readxl)
library(dplyr)
library(purrr)

# 定义需要提取的字段
required_columns <- c(
  "学校", "年级", "班级", "姓名", "出生日期", "性别",
  "身高（cm）", "体重（kg）", "收缩压（mmHg）", "舒张压（mmHg）",
  "肺活量（mL）", "谷丙转氨酶", "总胆红素", "总胆红素高低判断",
  "白细胞计数", "红细胞计数", "血红蛋白", "血小板计数"
)

# 定义读取数据函数
read_bmi_data <- function(year) {
  folder <- paste0(year, "-中小学")
  
  # 获取文件夹中所有Excel文件
  excel_files <- list.files(path = folder, 
                          pattern = "\\.(xlsx|xls)$", 
                          full.names = TRUE)
  
  if (length(excel_files) == 0) {
    warning(sprintf("%s文件夹中未找到Excel文件", folder))
    return(NULL)
  }
  
  # 读取所有Excel文件
  all_data <- map_df(excel_files, function(file) {
    # 获取所有sheet
    sheets <- excel_sheets(file)
    
    # 读取每个sheet
    sheet_data <- map_df(sheets, function(sheet) {
      tryCatch({
        data <- read_excel(file, sheet = sheet)
        if (nrow(data) > 0) {
          # 检查并只保留需要的列
          available_cols <- intersect(names(data), required_columns)
          if (length(available_cols) > 0) {
            data <- data[, available_cols]
            
            # 统一数据类型
            data <- data %>%
              mutate(across(c("身高（cm）", "体重（kg）", "收缩压（mmHg）", 
                            "舒张压（mmHg）", "肺活量（mL）", "谷丙转氨酶", 
                            "总胆红素", "白细胞计数", "红细胞计数", 
                            "血红蛋白", "血小板计数"), 
                          ~as.numeric(as.character(.))),
                     across(c("学校", "年级", "班级", "姓名",  
                            "性别", "总胆红素高低判断"), 
                          ~as.character(.)),
                     出生日期 = as.Date(出生日期, format = "%Y-%m-%d"))
            return(data)
          }
        }
      }, error = function(e) {
        warning(sprintf("文件 %s, sheet %s 读取失败: %s", basename(file), sheet, e$message))
        return(NULL)
      })
    })
    
    return(sheet_data)
  })
  
  # 添加年份并去重
  if (!is.null(all_data)) {
    all_data$year <- year
    # 根据关键字段去重，保留第一次出现的记录
    all_data <- all_data %>%
      distinct(学校, 年级, 班级, 姓名, 出生日期, .keep_all = TRUE)
  }
  
  return(all_data)
}

# 读取2020-2024年的数据
years <- 2020:2024
all_data <- map_df(years, read_bmi_data)

# 输出各年份数据的基本信息
cat("\n数据读取完成：\n")
all_data %>%
  group_by(year) %>%
  summarise(
    记录数 = n(),
    缺失列数 = sum(sapply(required_columns, function(col) !col %in% names(.))),
    .groups = "drop"
  ) %>%
  as.data.frame() %>%
  print()

# 创建索引
index <- all_data %>%
  select(学校, 姓名, 出生日期) %>%
  distinct() %>%
  mutate(id = row_number())

# 合并数据并移除姓名
data <- all_data %>%
  left_join(index, by = c("学校", "姓名", "出生日期")) %>%
  select(-姓名)

# 保存结果
write.csv(data, "outputs/cleaned_all_data.csv", row.names = FALSE)
write.csv(index, "outputs/cleaned_index.csv", row.names = FALSE)